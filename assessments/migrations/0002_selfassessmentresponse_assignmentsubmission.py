# Generated by Django 4.2.21 on 2025-07-10 22:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('assessments', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SelfAssessmentResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('responses', models.JSONField(help_text='User responses to assessment questions')),
                ('score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('completed_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('notes', models.TextField(blank=True, help_text='Additional notes from the user')),
                ('assessment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='assessments.selfassessment')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='self_assessment_responses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Self Assessment Response',
                'verbose_name_plural': 'Self Assessment Responses',
                'ordering': ['-completed_at'],
                'unique_together': {('assessment', 'user')},
            },
        ),
        migrations.CreateModel(
            name='AssignmentSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('submission_text', models.TextField(blank=True, help_text='Text submission content')),
                ('submission_file', models.FileField(blank=True, null=True, upload_to='assignments/')),
                ('submitted_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('status', models.CharField(choices=[('submitted', 'Submitted'), ('graded', 'Graded'), ('returned', 'Returned for Revision'), ('late', 'Late Submission')], default='submitted', max_length=20)),
                ('grade', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('feedback', models.TextField(blank=True, help_text='Instructor feedback')),
                ('graded_at', models.DateTimeField(blank=True, null=True)),
                ('is_late', models.BooleanField(default=False)),
                ('assignment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='assessments.assignment')),
                ('graded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='graded_assignments', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignment_submissions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Assignment Submission',
                'verbose_name_plural': 'Assignment Submissions',
                'ordering': ['-submitted_at'],
                'unique_together': {('assignment', 'student')},
            },
        ),
    ]
