"""
Django settings for blog project.

Generated by 'django-admin startproject' using Django 3.1.5.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.1/ref/settings/
"""

from pathlib import Path
import os
import sys
import socket
from django.contrib.messages import constants as messages

# =============================================================================
# SMART ENVIRONMENT DETECTION SYSTEM
# =============================================================================

# Import decouple for environment variable management
try:
    from decouple import config
    ENV_FILE_LOADED = True
except ImportError:
    # Fallback to os.environ if decouple is not available
    config = os.environ.get
    ENV_FILE_LOADED = False

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

def detect_environment():
    """
    Smart environment detection - Simple but effective approach
    Returns: True if production, False if development
    """

    # Method 1: Explicit environment variable (highest priority)
    env_setting = os.environ.get('DJANGO_ENV', '').lower()
    if env_setting == 'production':
        return True
    elif env_setting == 'development':
        return False

    # Method 2: Check for production platform indicators
    production_indicators = [
        os.environ.get('RENDER'),           # Render.com
        os.environ.get('HEROKU_APP_NAME'),  # Heroku
        os.environ.get('RAILWAY_ENVIRONMENT'), # Railway
        os.environ.get('VERCEL'),           # Vercel
        'neon.tech' in os.environ.get('DB_HOST', ''), # Neon database
    ]

    if any(production_indicators):
        return True

    # Method 3: Check if running with runserver (development)
    if 'runserver' in sys.argv:
        return False

    # Method 4: Check DEBUG setting from environment
    debug_setting = os.environ.get('DEBUG', 'True').lower()
    if debug_setting == 'false':
        return True

    # Default to development for safety
    return False

# Detect environment
IS_PRODUCTION = detect_environment()
IS_DEVELOPMENT = not IS_PRODUCTION

# Environment indicator
print(f"🔧 YITP Environment: {'PRODUCTION' if IS_PRODUCTION else 'DEVELOPMENT'}")

# =============================================================================
# CORE DJANGO SETTINGS
# =============================================================================

# Secret Key
if IS_PRODUCTION:
    SECRET_KEY = config('SECRET_KEY', default='yitp-production-key-change-this-immediately')
else:
    SECRET_KEY = config('SECRET_KEY', default='yitp-dev-key-not-for-production-use-only')

# Debug Mode
DEBUG = not IS_PRODUCTION

# Allowed Hosts
if IS_PRODUCTION:
    ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='yitp-django-app.onrender.com,*.onrender.com').split(',')
else:
    ALLOWED_HOSTS = ['*']  # Permissive for development

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# =============================================================================
# COURSE BUILDER CONFIGURATION
# =============================================================================

# TinyMCE API Key for Course Builder
TINYMCE_API_KEY = os.getenv('TINYMCE_API_KEY', 'qu2jb8k2dyah1y5pjdglgob206f26juotj3u82hzd7mvyz1x')



# =============================================================================
# SECURITY SETTINGS - ENVIRONMENT AWARE
# =============================================================================

if IS_PRODUCTION:
    # Production Security Settings
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    X_FRAME_OPTIONS = 'DENY'
else:
    # Development Security Settings - Relaxed for local development
    SECURE_HSTS_SECONDS = 0
    SECURE_SSL_REDIRECT = False
    SESSION_COOKIE_SECURE = False
    CSRF_COOKIE_SECURE = False
    SECURE_BROWSER_XSS_FILTER = False
    SECURE_CONTENT_TYPE_NOSNIFF = False
    X_FRAME_OPTIONS = 'SAMEORIGIN'
    SECURE_CONTENT_TYPE_NOSNIFF = True
#

# Application definition

INSTALLED_APPS = [
    'jet',
    'graphene_django',
    'users',  # Simplified from 'users.apps.UsersConfig'
    'yitp',
    'blogapp',  # Fixed to match actual directory name (blogapp)
    'events',
       # New LMS apps
    'courses',
    'progress',
    'assessments',
    'communication',
    'content',
    'payments',  # Added for test suite
    'certificates',  # Added for test suite
    'analytics',  # Payment analytics dashboard
    'course_builder',  # Course creation wizard

    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',
    'ckeditor',
    'taggit',
    'import_export',
    'ckeditor_uploader',

    # Third-party apps
   'rest_framework',
   'crispy_forms',
   'crispy_bootstrap5',
   'corsheaders',
]



MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # YITP Unified System Middleware
    'yitp.middleware.SmartRedirectMiddleware',
    'yitp.middleware.CourseDiscoveryRedirectMiddleware',
    'yitp.middleware.WelcomePageRedirectMiddleware',
    'yitp.middleware.UnifiedNavigationMiddleware',
]

# Environment-specific middleware adjustments
if IS_DEVELOPMENT:
    # Remove security middleware that might interfere with development
    MIDDLEWARE = [m for m in MIDDLEWARE if 'SecurityMiddleware' not in m]
    print("🔧 Removed SecurityMiddleware for development")

ROOT_URLCONF = 'blog.urls'

# Sites framework
SITE_ID = 1

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR,'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'blog.wsgi.application'

WHITENOISE_MANIFEST_STRICT = False
# Database
# https://docs.djangoproject.com/en/3.1/ref/settings/#databases

# =============================================================================
# DATABASE CONFIGURATION - ENVIRONMENT AWARE
# =============================================================================

if IS_PRODUCTION:
    # Production: PostgreSQL (Neon) Database
    print("📊 Using PostgreSQL database for production")
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.getenv('DB_NAME', 'yitplms'),
            'USER': os.getenv('DB_USER', 'yitplms_owner'),
            'PASSWORD': os.getenv('DB_PASSWORD', 'npg_LwHI4a8TufWb'),
            'HOST': os.getenv('DB_HOST', 'ep-spring-block-a5drxziv-pooler.us-east-2.aws.neon.tech'),
            'PORT': os.getenv('DB_PORT', '5432'),
            'OPTIONS': {
                'sslmode': 'require',
                'connect_timeout': 30,
            },
        }
    }
else:
    # Development: SQLite Database
    print("📊 Using SQLite database for development")
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db_development.sqlite3',
        }
    }




AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Africa/Nairobi'  # This is the correct time zone for East Africa (UTC+3)

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.1/howto/static-files/



# =============================================================================
# STATIC FILES CONFIGURATION - ENVIRONMENT AWARE
# =============================================================================

STATIC_URL = '/static/'

# Additional directories to look for static files
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

if IS_PRODUCTION:
    # Production: Collect static files for deployment
    print("📁 Using production static files configuration")
    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
    STATICFILES_STORAGE = "django.contrib.staticfiles.storage.StaticFilesStorage"

    # Ensure staticfiles directory exists
    if not os.path.exists(STATIC_ROOT):
        os.makedirs(STATIC_ROOT, exist_ok=True)
else:
    # Development: Serve static files directly
    print("📁 Using development static files configuration")
    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')  # Still needed for collectstatic

    # Ensure staticfiles directory exists to prevent warnings
    if not os.path.exists(STATIC_ROOT):
        os.makedirs(STATIC_ROOT, exist_ok=True)

# Media files (user-uploaded files)
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media/')

# Template settings

## For media files
UPLOADCARE = {
  # Don’t forget to set real keys when it gets real :)

  'pub_key': '18ad19435c41077ca842',
  'secret': 'b27f8995d2e4b66cbf02',
}

# ============================================================================
# CKEDITOR CONFIGURATION FOR RICH CONTENT CREATION
# ============================================================================

CKEDITOR_UPLOAD_PATH = "uploads/"
CKEDITOR_IMAGE_BACKEND = "pillow"
CKEDITOR_JQUERY_URL = 'https://ajax.googleapis.com/ajax/libs/jquery/2.2.4/jquery.min.js'

CKEDITOR_CONFIGS = {
    'default': {
        'toolbar': 'full',
        'height': 400,
        'width': '100%',
        'extraPlugins': ','.join([
            'uploadimage',
            'div',
            'autolink',
            'autoembed',
            'embedsemantic',
            'autogrow',
            'widget',
            'lineutils',
            'clipboard',
            'dialog',
            'dialogui',
            'elementspath'
        ]),
        'removePlugins': 'stylesheetparser',
        'allowedContent': True,
        'toolbar_full': [
            ['Styles', 'Format', 'Bold', 'Italic', 'Underline', 'Strike', 'SpellChecker', 'Undo', 'Redo'],
            ['Link', 'Unlink', 'Anchor'],
            ['Image', 'Flash', 'Table', 'HorizontalRule'],
            ['TextColor', 'BGColor'],
            ['Smiley', 'SpecialChar'], ['Source'],
            ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
            ['NumberedList', 'BulletedList'],
            ['Indent', 'Outdent'],
            ['Maximize'],
        ],
        'stylesSet': [
            {'name': 'YITP Highlight', 'element': 'span', 'styles': {'background-color': '#fff8f5', 'color': '#ff5d15', 'padding': '2px 4px', 'border-radius': '3px'}},
            {'name': 'YITP Alert', 'element': 'div', 'styles': {'background-color': '#fff8f5', 'border-left': '4px solid #ff5d15', 'padding': '10px', 'margin': '10px 0'}},
            {'name': 'Code Block', 'element': 'pre', 'styles': {'background-color': '#f8f9fa', 'border': '1px solid #e9ecef', 'padding': '10px', 'border-radius': '5px'}},
        ],
    },
    'lesson_content': {
        'toolbar': 'full',
        'height': 500,
        'width': '100%',
        'extraPlugins': ','.join([
            'uploadimage',
            'div',
            'autolink',
            'autoembed',
            'embedsemantic',
            'autogrow',
            'widget',
            'lineutils',
            'clipboard',
            'dialog',
            'dialogui',
            'elementspath',
            'codesnippet'
        ]),
        'removePlugins': 'stylesheetparser',
        'allowedContent': True,
        'codeSnippet_theme': 'monokai_sublime',
        'toolbar_full': [
            ['Styles', 'Format', 'Bold', 'Italic', 'Underline', 'Strike'],
            ['TextColor', 'BGColor'],
            ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
            ['NumberedList', 'BulletedList', 'Indent', 'Outdent'],
            ['Link', 'Unlink', 'Anchor'],
            ['Image', 'Table', 'HorizontalRule', 'CodeSnippet'],
            ['Undo', 'Redo'],
            ['Source', 'Maximize'],
        ],
        'stylesSet': [
            {'name': 'Learning Objective', 'element': 'div', 'styles': {'background-color': '#e8f5e8', 'border-left': '4px solid #28a745', 'padding': '15px', 'margin': '15px 0', 'border-radius': '5px'}},
            {'name': 'Important Note', 'element': 'div', 'styles': {'background-color': '#fff3cd', 'border-left': '4px solid #ffc107', 'padding': '15px', 'margin': '15px 0', 'border-radius': '5px'}},
            {'name': 'YITP Highlight', 'element': 'span', 'styles': {'background-color': '#fff8f5', 'color': '#ff5d15', 'padding': '2px 6px', 'border-radius': '3px', 'font-weight': 'bold'}},
            {'name': 'Exercise Box', 'element': 'div', 'styles': {'background-color': '#f0f8ff', 'border': '2px solid #1a2e53', 'padding': '20px', 'margin': '20px 0', 'border-radius': '10px'}},
            {'name': 'Code Inline', 'element': 'code', 'styles': {'background-color': '#f8f9fa', 'color': '#e83e8c', 'padding': '2px 4px', 'border-radius': '3px', 'font-family': 'monospace'}},
        ],
    },
    'basic': {
        'toolbar': 'basic',
        'height': 200,
        'width': '100%',
        'toolbar_basic': [
            ['Bold', 'Italic', 'Underline'],
            ['NumberedList', 'BulletedList'],
            ['Link', 'Unlink'],
            ['RemoveFormat', 'Source']
        ],
    }
}


# ============================================================================
# AUTHENTICATION & LOGIN CONFIGURATION
# ============================================================================

# Login and logout URLs with instructor-aware redirects
LOGIN_URL = '/login/'
LOGOUT_URL = '/logout/'
LOGIN_REDIRECT_URL = '/'  # Default redirect, will be overridden by custom logic
LOGOUT_REDIRECT_URL = '/'

JET_DEFAULT_THEME = 'green'
JET_THEMES = [
    {
        'theme': 'default',
        'color': '#47bac1',
        'title': 'Default'
    },
    {
        'theme': 'green',
        'color': '#44b78b',
        'title': 'Green'
    },
    {
        'theme': 'light-green',
        'color': '#2faa60',
        'title': 'Light Green'
    },
    {
        'theme': 'light-violet',
        'color': '#a464c4',
        'title': 'Light Violet'
    },
    {
        'theme': 'light-blue',
        'color': '#5EADDE',
        'title': 'Light Blue'
    },
    {
        'theme': 'light-gray',
        'color': '#222',
        'title': 'Light Gray'
    }
]

# Jet Side Menu Settings
JET_SIDE_MENU_COMPACT = True

JET_SIDE_MENU_ITEMS = [
    {'label': 'Blog Management', 'items': [
        {'name': 'blogapp.post', 'label': 'Posts'},
        {'name': 'blogapp.category', 'label': 'Categories'},
        {'name': 'blogapp.comment', 'label': 'Comments'},
        {'name': 'blogapp.staticcontent', 'label': 'Static Content'},
    ]},
    {'label': 'Users', 'items': [
        {'name': 'auth.user'},
        {'name': 'auth.group'},
    ]},
]

# Additional Jet settings
JET_CHANGE_FORM_SIBLING_LINKS = True
JET_INDEX_DASHBOARD = 'jet.dashboard.dashboard.DefaultIndexDashboard'

# Django Messages Framework Configuration
MESSAGE_TAGS = {
    messages.DEBUG: 'debug',
    messages.INFO: 'info',
    messages.SUCCESS: 'success',
    messages.WARNING: 'warning',
    messages.ERROR: 'error',
}

# Message storage backend
MESSAGE_STORAGE = 'django.contrib.messages.storage.session.SessionStorage'

# =============================================================================
# EMAIL CONFIGURATION - ENVIRONMENT AWARE
# =============================================================================

if IS_PRODUCTION:
    # Production: Gmail SMTP Backend
    print("📧 Using Gmail SMTP for production email")
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST = 'smtp.gmail.com'
    EMAIL_PORT = 587
    EMAIL_USE_TLS = True
    EMAIL_USE_SSL = False
    EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '<EMAIL>')
    EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', 'roqu frlt wvof rqxk')
    EMAIL_TIMEOUT = 30

    # SSL certificate handling for production
    import ssl
    if os.getenv('DJANGO_DEVELOPMENT'):
        ssl._create_default_https_context = ssl._create_unverified_context
else:
    # Development: Console Email Backend
    print("📧 Using console email backend for development")
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
    EMAIL_HOST_USER = '<EMAIL>'
    EMAIL_HOST_PASSWORD = 'development-password'
    EMAIL_TIMEOUT = 10

DEFAULT_FROM_EMAIL = f'YOUTH IMPACT GLOBAL <{EMAIL_HOST_USER}>'

# Admin email for notifications
ADMIN_EMAIL = '<EMAIL>'

# OTP Configuration
OTP_EXPIRY_MINUTES = 200
OTP_LENGTH = 6

# =============================================================================
# M-PESA PAYMENT INTEGRATION CONFIGURATION
# =============================================================================

# M-Pesa API Credentials
# Note: These are development/sandbox credentials. For production, use environment variables
MPESA_CONSUMER_KEY = os.environ.get('MPESA_CONSUMER_KEY', 'UMi2MLMFIdaOS8vRFiWLG40CJ4GzWAGAHbwFROxe473iZ6gQ')
MPESA_CONSUMER_SECRET = os.environ.get('MPESA_CONSUMER_SECRET', '2K3IJkdE7uxLJm9Nis3mhO3TZHqmowc0ndI9abTGxGJ4gcAdvdAZ5C9tx9wrAWRp')

# M-Pesa Business Configuration
MPESA_SHORTCODE = os.environ.get('MPESA_SHORTCODE', '174379')  # Sandbox shortcode
MPESA_PASSKEY = os.environ.get('MPESA_PASSKEY', 'bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919')  # Sandbox passkey

# M-Pesa API URLs (Sandbox vs Production)
MPESA_ENVIRONMENT = os.environ.get('MPESA_ENVIRONMENT', 'sandbox')  # 'sandbox' or 'production'

if MPESA_ENVIRONMENT == 'production':
    MPESA_BASE_URL = 'https://api.safaricom.co.ke'
else:
    MPESA_BASE_URL = 'https://sandbox.safaricom.co.ke'

# M-Pesa API Endpoints
MPESA_AUTH_URL = f'{MPESA_BASE_URL}/oauth/v1/generate?grant_type=client_credentials'
MPESA_STK_PUSH_URL = f'{MPESA_BASE_URL}/mpesa/stkpush/v1/processrequest'
MPESA_QUERY_URL = f'{MPESA_BASE_URL}/mpesa/stkpushquery/v1/query'

# M-Pesa Callback URLs
# These URLs will receive payment notifications from Safaricom
SITE_URL = os.environ.get('SITE_URL', 'https://yitp-lms.onrender.com')  # Production domain for YITP LMS
MPESA_CALLBACK_URL = f'{SITE_URL}/payments/mpesa/callback/'
MPESA_RESULT_URL = f'{SITE_URL}/payments/mpesa/result/'
MPESA_TIMEOUT_URL = f'{SITE_URL}/payments/mpesa/timeout/'

# Payment Configuration
PAYMENT_TIMEOUT_HOURS = 24  # Payment expires after 24 hours
PAYMENT_CURRENCY = 'KES'
PAYMENT_PROCESSING_FEE_PERCENTAGE = 0.00  # No processing fee for now

# Bank Transfer Details (for manual payments)
BANK_TRANSFER_DETAILS = {
    'bank_name': 'Absa Bank Kenya',
    'account_name': 'Youth Impact Training Programme',
    'account_number': '**********',
    'paybill_number': '303030',
    'branch_code': 'ABSAKENAXXX',
    'swift_code': 'ABSAKENAXXX',
    'verification_phone': '+************',  # WhatsApp number for payment verification
}

# Payment Method Configuration
AVAILABLE_PAYMENT_METHODS = [
    {
        'code': 'mpesa',
        'name': 'M-Pesa',
        'description': 'Pay using M-Pesa mobile money',
        'enabled': True,
        'requires_phone': True,
        'processing_fee': 0.00,
        'supports_installments': True,
    },
    {
        'code': 'bank_transfer',
        'name': 'Bank Transfer',
        'description': 'Direct bank transfer to YITP account',
        'enabled': True,
        'requires_account_number': False,
        'processing_fee': 0.00,
        'supports_installments': True,
    },
    {
        'code': 'paypal',
        'name': 'PayPal',
        'description': 'Pay securely with PayPal',
        'enabled': True,
        'requires_verification': True,
        'processing_fee': 0.00,
        'supports_installments': True,
    },
    {
        'code': 'card',
        'name': 'Credit/Debit Card',
        'description': 'Pay using Visa, Mastercard, or other cards',
        'enabled': False,  # To be implemented later with Stripe
        'processing_fee': 2.50,
        'supports_installments': False,
    }
]

# Security Settings for Payment Processing
PAYMENT_SECURITY = {
    'max_payment_attempts': 3,
    'payment_verification_required': True,
    'admin_approval_required_above': 50000,  # KES amount requiring admin approval
    'auto_refund_timeout_hours': 72,
}

# =============================================================================
# CERTIFICATE GENERATION CONFIGURATION
# =============================================================================

# Certificate Settings
CERTIFICATE_SETTINGS = {
    'template_path': 'certificates/certificate_template.html',
    'pdf_generation_enabled': True,
    'verification_url_base': f'{SITE_URL}/certificates/verify/',
    'certificate_storage_path': 'certificates/',
    'watermark_enabled': True,
    'digital_signature_enabled': False,  # To be implemented later
}

# Certificate Branding
CERTIFICATE_BRANDING = {
    'organization_name': 'Youth Impact Training Programme',
    'logo_path': 'images/yitp-logo.png',
    'signature_path': 'images/director-signature.png',
    'primary_color': '#1a2e53',  # YITP dark blue
    'secondary_color': '#ff5d15',  # YITP orange
    'font_family': 'Arial, sans-serif',
}

# =============================================================================
# LOGGING CONFIGURATION FOR PAYMENT AND ENROLLMENT TRACKING
# =============================================================================

# Enhanced logging for payment and enrollment operations
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'yitp.log'),
            'formatter': 'verbose',
        },
        'payment_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'payments.log'),
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG' if DEBUG else 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'payments': {
            'handlers': ['payment_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'courses.enrollment_service': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'certificates': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Create logs directory if it doesn't exist
LOGS_DIR = os.path.join(BASE_DIR, 'logs')
if not os.path.exists(LOGS_DIR):
    os.makedirs(LOGS_DIR)

# =============================================================================
# DJANGO CRISPY FORMS CONFIGURATION
# =============================================================================
# Configure django-crispy-forms to use Bootstrap 5
CRISPY_TEMPLATE_PACK = 'bootstrap5'
CRISPY_ALLOWED_TEMPLATE_PACKS = ('bootstrap5',)

# =============================================================================
# PRODUCTION SECURITY NOTES
# =============================================================================
"""
IMPORTANT: For production deployment, ensure the following:

1. Set environment variables for sensitive data:
   - MPESA_CONSUMER_KEY
   - MPESA_CONSUMER_SECRET
   - MPESA_PASSKEY
   - MPESA_SHORTCODE (your actual business shortcode)
   - SITE_URL (your production domain)

2. Update MPESA_ENVIRONMENT to 'production'

3. Replace sandbox credentials with actual production credentials from Safaricom

4. Ensure HTTPS is enabled for all callback URLs

5. Implement proper error monitoring and alerting

6. Set up database backups for payment records

7. Configure proper firewall rules for M-Pesa callback IPs

Example production environment variables:
export MPESA_CONSUMER_KEY="your_production_consumer_key"
export MPESA_CONSUMER_SECRET="your_production_consumer_secret"
export MPESA_SHORTCODE="your_business_shortcode"
export MPESA_PASSKEY="your_production_passkey"
export MPESA_ENVIRONMENT="production"
export SITE_URL="https://yourdomain.com"
"""

# =============================================================================
# CONFIGURATION SUMMARY & ENVIRONMENT SWITCHING
# =============================================================================

print("=" * 60)
print(f"🚀 YITP LMS CONFIGURATION SUMMARY")
print("=" * 60)
print(f"🔧 Environment: {'PRODUCTION' if IS_PRODUCTION else 'DEVELOPMENT'}")
print(f"🐛 Debug Mode: {DEBUG}")
print(f"📊 Database: {'PostgreSQL (Neon)' if IS_PRODUCTION else 'SQLite (Local)'}")
print(f"📧 Email Backend: {'Gmail SMTP' if IS_PRODUCTION else 'Console'}")
print(f"🔒 Security: {'Production (HTTPS)' if IS_PRODUCTION else 'Development (HTTP)'}")
print(f"📁 Static Files: {'Production (Collected)' if IS_PRODUCTION else 'Development (Direct)'}")
print(f"🌐 Allowed Hosts: {ALLOWED_HOSTS}")
print("=" * 60)

# Environment switching instructions
if IS_DEVELOPMENT:
    print("💡 DEVELOPMENT MODE ACTIVE")
    print("   • Using SQLite database for local development")
    print("   • Using console email backend (emails printed to terminal)")
    print("   • Security settings relaxed for HTTP development server")
    print("   • Static files served directly by Django")
    print("")
    print("🚀 To switch to PRODUCTION mode:")
    print("   • Set environment variable: export DJANGO_ENV=production")
    print("   • Or deploy to production platform (Render, Heroku, etc.)")
    print("   • Or ensure production environment variables are set")
else:
    print("🚀 PRODUCTION MODE ACTIVE")
    print("   • Using PostgreSQL database (Neon)")
    print("   • Using Gmail SMTP for email delivery")
    print("   • Production security settings enabled")
    print("   • Static files collected for deployment")
    print("")
    print("🛠️ To switch to DEVELOPMENT mode:")
    print("   • Set environment variable: export DJANGO_ENV=development")
    print("   • Or run locally with: python manage.py runserver")
    print("   • Or unset production environment variables")

print("=" * 60)
print("✅ YITP LMS: CONFIGURATION COMPLETE")
print("=" * 60)