# Generated by Django 4.2.21 on 2025-07-09 00:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(unique=True)),
                ('mpesa_payments', models.IntegerField(default=0)),
                ('bank_transfer_payments', models.IntegerField(default=0)),
                ('paypal_payments', models.IntegerField(default=0)),
                ('total_payments', models.IntegerField(default=0)),
                ('mpesa_revenue', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('bank_transfer_revenue', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('paypal_revenue', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('confirmed_payments', models.IntegerField(default=0)),
                ('pending_payments', models.IntegerField(default=0)),
                ('failed_payments', models.IntegerField(default=0)),
                ('expired_payments', models.IntegerField(default=0)),
                ('installment_payments', models.IntegerField(default=0)),
                ('first_installments', models.IntegerField(default=0)),
                ('second_installments', models.IntegerField(default=0)),
                ('installment_completion_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('new_users', models.IntegerField(default=0)),
                ('returning_users', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Payment Analytics',
                'verbose_name_plural': 'Payment Analytics',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='UserPaymentBehavior',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_payments', models.IntegerField(default=0)),
                ('successful_payments', models.IntegerField(default=0)),
                ('failed_payments', models.IntegerField(default=0)),
                ('preferred_payment_method', models.CharField(blank=True, max_length=20)),
                ('mpesa_usage_count', models.IntegerField(default=0)),
                ('bank_transfer_usage_count', models.IntegerField(default=0)),
                ('paypal_usage_count', models.IntegerField(default=0)),
                ('total_amount_paid', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('average_payment_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('installment_payments_made', models.IntegerField(default=0)),
                ('installment_completion_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('first_payment_date', models.DateTimeField(blank=True, null=True)),
                ('last_payment_date', models.DateTimeField(blank=True, null=True)),
                ('average_days_between_payments', models.IntegerField(default=0)),
                ('payment_failure_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('late_payment_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='payment_behavior', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Payment Behavior',
                'verbose_name_plural': 'User Payment Behaviors',
            },
        ),
        migrations.CreateModel(
            name='CourseRevenueAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_enrollments', models.IntegerField(default=0)),
                ('paid_enrollments', models.IntegerField(default=0)),
                ('free_enrollments', models.IntegerField(default=0)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('average_revenue_per_user', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('mpesa_revenue', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('bank_transfer_revenue', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('paypal_revenue', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('installment_enrollments', models.IntegerField(default=0)),
                ('installment_completion_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('conversion_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='revenue_analytics', to='courses.course')),
            ],
            options={
                'verbose_name': 'Course Revenue Analytics',
                'verbose_name_plural': 'Course Revenue Analytics',
            },
        ),
    ]
