# Generated by Django 4.2.21 on 2025-07-19 16:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0002_alter_lesson_content'),
    ]

    operations = [
        migrations.CreateModel(
            name='QuestionBank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_text', models.TextField()),
                ('question_type', models.CharField(choices=[('multiple_choice', 'Multiple Choice'), ('true_false', 'True/False'), ('short_answer', 'Short Answer'), ('essay', 'Essay'), ('matching', 'Matching'), ('fill_blank', 'Fill in the Blank')], max_length=20)),
                ('options', models.J<PERSON><PERSON><PERSON>(blank=True, default=list, help_text='Options for multiple choice questions')),
                ('correct_answer', models.TextField(help_text='Correct answer or answer key')),
                ('explanation', models.TextField(blank=True, help_text='Explanation for the correct answer')),
                ('points', models.IntegerField(default=1)),
                ('difficulty_level', models.CharField(choices=[('easy', 'Easy'), ('medium', 'Medium'), ('hard', 'Hard')], default='medium', max_length=20)),
                ('subject_area', models.CharField(blank=True, max_length=100)),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500)),
                ('is_public', models.BooleanField(default=False, help_text='Available to all instructors')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_questions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Question Bank Item',
                'verbose_name_plural': 'Question Bank',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CourseTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('template_type', models.CharField(choices=[('basic', 'Basic Course'), ('video_series', 'Video Series'), ('workshop', 'Workshop'), ('certification', 'Certification Program'), ('mini_course', 'Mini Course')], default='basic', max_length=20)),
                ('structure', models.JSONField(help_text='Course outline template with modules and lessons')),
                ('estimated_duration', models.IntegerField(default=10, help_text='Estimated duration in hours')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='courses.category')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_course_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Course Template',
                'verbose_name_plural': 'Course Templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CourseBuilderSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_data', models.JSONField(default=dict, help_text='Auto-saved course data')),
                ('current_step', models.IntegerField(default=1, help_text='Current wizard step (1-5)')),
                ('status', models.CharField(choices=[('active', 'Active'), ('completed', 'Completed'), ('abandoned', 'Abandoned')], default='active', max_length=20)),
                ('last_saved', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('course', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='courses.course')),
                ('instructor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='course_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Course Builder Session',
                'verbose_name_plural': 'Course Builder Sessions',
                'ordering': ['-last_saved'],
            },
        ),
        migrations.CreateModel(
            name='ContentBlock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content_type', models.CharField(choices=[('text', 'Text Content'), ('video', 'Video'), ('image', 'Image'), ('document', 'Document'), ('quiz', 'Quiz'), ('exercise', 'Interactive Exercise'), ('code', 'Code Block'), ('callout', 'Callout/Note')], max_length=20)),
                ('content', models.JSONField(help_text='Content data structure')),
                ('description', models.TextField(blank=True)),
                ('is_template', models.BooleanField(default=False, help_text='Available as template for other instructors')),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_content_blocks', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Content Block',
                'verbose_name_plural': 'Content Blocks',
                'ordering': ['-created_at'],
            },
        ),
    ]
