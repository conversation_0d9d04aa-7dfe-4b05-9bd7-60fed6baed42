# Generated by Django 4.2.21 on 2025-07-05 16:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='payment',
            name='installment_sequence',
            field=models.PositiveSmallIntegerField(blank=True, help_text='Installment sequence (1 for first, 2 for second)', null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='is_installment',
            field=models.BooleanField(default=False, help_text='Whether this is an installment payment'),
        ),
        migrations.AddField(
            model_name='payment',
            name='paypal_payer_id',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='paypal_payment_id',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='related_payment',
            field=models.ForeignKey(blank=True, help_text='Link to related installment payment', null=True, on_delete=django.db.models.deletion.SET_NULL, to='payments.payment'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='payment_method',
            field=models.CharField(choices=[('mpesa', 'M-Pesa'), ('bank_transfer', 'Bank Transfer'), ('paypal', 'PayPal'), ('card', 'Credit/Debit Card'), ('cash', 'Cash Payment'), ('other', 'Other')], max_length=20),
        ),
        migrations.AlterField(
            model_name='paymentnotification',
            name='notification_type',
            field=models.CharField(choices=[('payment_pending', 'Payment Pending'), ('payment_confirmed', 'Payment Confirmed'), ('payment_failed', 'Payment Failed'), ('payment_expired', 'Payment Expired'), ('payment_reminder', 'Payment Reminder'), ('installment_confirmed', 'First Installment Confirmed'), ('installment_reminder', 'Second Installment Reminder'), ('installment_expiring', 'Installment Access Expiring'), ('installment_expired', 'Installment Access Expired'), ('sponsorship_approved', 'Sponsorship Approved'), ('sponsorship_rejected', 'Sponsorship Rejected')], max_length=25),
        ),
    ]
