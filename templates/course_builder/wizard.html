{% extends 'yitp/base.html' %}
{% load static %}

{% block title %}Course Creation Wizard - YITP{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css" rel="stylesheet">
<style>
    .wizard-container {
        background: #f8f9fa;
        min-height: 100vh;
        padding-top: 100px;
    }
    
    .wizard-header {
        background: white;
        border-bottom: 1px solid #dee2e6;
        padding: 1rem 0;
        margin-bottom: 2rem;
    }
    
    .step-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    .step {
        display: flex;
        align-items: center;
        margin: 0 1rem;
    }
    
    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #dee2e6;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 0.5rem;
    }
    
    .step.active .step-number {
        background: #ff5d15;
        color: white;
    }
    
    .step.completed .step-number {
        background: #28a745;
        color: white;
    }
    
    .step-title {
        font-weight: 600;
        color: #495057;
    }
    
    .step.active .step-title {
        color: #ff5d15;
    }
    
    .wizard-content {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    
    .module-item, .lesson-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        cursor: move;
        transition: all 0.3s ease;
    }
    
    .module-item:hover, .lesson-item:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .module-item.sortable-ghost, .lesson-item.sortable-ghost {
        opacity: 0.5;
    }
    
    .drag-handle {
        color: #6c757d;
        cursor: move;
        margin-right: 0.5rem;
    }
    
    .btn-add-module, .btn-add-lesson {
        border: 2px dashed #dee2e6;
        background: transparent;
        color: #6c757d;
        padding: 1rem;
        border-radius: 8px;
        width: 100%;
        transition: all 0.3s ease;
    }
    
    .btn-add-module:hover, .btn-add-lesson:hover {
        border-color: #ff5d15;
        color: #ff5d15;
        background: rgba(255, 93, 21, 0.05);
    }
    
    .wizard-navigation {
        background: white;
        border-top: 1px solid #dee2e6;
        padding: 1rem 0;
        position: sticky;
        bottom: 0;
    }
    
    .content-editor {
        min-height: 300px;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
    }
    
    .question-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .auto-save-indicator {
        position: fixed;
        top: 120px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .auto-save-indicator.show {
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="wizard-container">
    <!-- Auto-save indicator -->
    <div id="autoSaveIndicator" class="auto-save-indicator">
        <i class="fas fa-check me-1"></i>
        Auto-saved
    </div>

    <!-- Wizard Header -->
    <div class="wizard-header">
        <div class="container">
            <div class="step-indicator">
                {% for step in steps %}
                <div class="step {% if step.number == current_step %}active{% elif step.number < current_step %}completed{% endif %}">
                    <div class="step-number">
                        {% if step.number < current_step %}
                            <i class="fas fa-check"></i>
                        {% else %}
                            {{ step.number }}
                        {% endif %}
                    </div>
                    <div class="step-info d-none d-md-block">
                        <div class="step-title">{{ step.title }}</div>
                        <div class="step-description small text-muted">{{ step.description }}</div>
                    </div>
                </div>
                {% if not forloop.last %}
                    <div class="step-connector" style="width: 50px; height: 2px; background: #dee2e6; margin: 0 1rem;"></div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="container">
        <div class="wizard-content">
            <!-- Step 1: Course Basics -->
            {% if current_step == 1 %}
            <div id="step1" class="wizard-step">
                <h2 class="mb-4">Course Basics</h2>
                <form id="courseBasicsForm">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="courseTitle" class="form-label">Course Title *</label>
                                <input type="text" class="form-control" id="courseTitle" name="title" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="courseDescription" class="form-label">Course Description *</label>
                                <textarea class="form-control" id="courseDescription" name="description" rows="4" required></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="courseCategory" class="form-label">Category</label>
                                        <select class="form-select" id="courseCategory" name="category">
                                            <option value="">Select Category</option>
                                            {% for category in categories %}
                                            <option value="{{ category.id }}">{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="difficultyLevel" class="form-label">Difficulty Level</label>
                                        <select class="form-select" id="difficultyLevel" name="difficulty_level">
                                            <option value="beginner">Beginner</option>
                                            <option value="intermediate">Intermediate</option>
                                            <option value="advanced">Advanced</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="estimatedDuration" class="form-label">Estimated Duration (hours)</label>
                                <input type="number" class="form-control" id="estimatedDuration" name="estimated_duration" value="10" min="1">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <h5>Course Templates</h5>
                            <p class="text-muted small">Start with a pre-built template to save time</p>
                            {% for template in course_templates|slice:":4" %}
                            <div class="template-option mb-2 p-2 border rounded cursor-pointer" onclick="loadTemplate({{ template.id }})">
                                <strong>{{ template.name }}</strong>
                                <br>
                                <small class="text-muted">{{ template.description|truncatewords:8 }}</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </form>
            </div>
            {% endif %}

            <!-- Step 2: Course Structure -->
            {% if current_step == 2 %}
            <div id="step2" class="wizard-step">
                <h2 class="mb-4">Course Structure</h2>
                <p class="text-muted mb-4">Organize your course into modules and lessons. Drag and drop to reorder.</p>
                
                <div id="courseStructure">
                    <!-- Modules will be dynamically added here -->
                </div>
                
                <button type="button" class="btn btn-add-module" onclick="addModule()">
                    <i class="fas fa-plus me-2"></i>
                    Add Module
                </button>
            </div>
            {% endif %}

            <!-- Step 3: Content Creation -->
            {% if current_step == 3 %}
            <div id="step3" class="wizard-step">
                <h2 class="mb-4">Content Creation</h2>
                <p class="text-muted mb-4">Add content to your lessons using our rich text editor.</p>

                <div class="row">
                    <div class="col-lg-8">
                        <div id="contentCreation">
                            <div class="mb-3">
                                <label for="lessonSelector" class="form-label">Select Lesson to Edit</label>
                                <select class="form-select" id="lessonSelector" onchange="loadLessonContent()">
                                    <option value="">Choose a lesson...</option>
                                    <!-- Lessons will be populated dynamically -->
                                </select>
                            </div>

                            <div id="lessonContentEditor" style="display: none;">
                                <div class="mb-3">
                                    <label for="lessonContent" class="form-label">Lesson Content</label>
                                    <textarea id="lessonContent" name="content" class="tinymce">
                                        <h3>Welcome to this lesson!</h3>
                                        <p>Start creating your engaging content here...</p>
                                    </textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="videoUrl" class="form-label">Video URL (Optional)</label>
                                            <input type="url" class="form-control" id="videoUrl" placeholder="https://youtube.com/watch?v=...">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="lessonDuration" class="form-label">Estimated Duration (minutes)</label>
                                            <input type="number" class="form-control" id="lessonDuration" value="30" min="1">
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex gap-2 mb-3">
                                    <button type="button" class="btn btn-outline-primary" onclick="openMediaLibrary()">
                                        <i class="fas fa-images me-2"></i>Media Library
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="insertContentBlock()">
                                        <i class="fas fa-puzzle-piece me-2"></i>Insert Template
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="saveLessonContent()">
                                        <i class="fas fa-save me-2"></i>Save Content
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Content Templates</h5>
                            </div>
                            <div class="card-body">
                                {% for block in content_blocks|slice:":5" %}
                                <div class="template-block mb-2 p-2 border rounded cursor-pointer" onclick="insertTemplate({{ block.id }})">
                                    <strong>{{ block.title }}</strong>
                                    <br>
                                    <small class="text-muted">{{ block.get_content_type_display }}</small>
                                </div>
                                {% endfor %}
                                <a href="{% url 'course_builder:content_templates' %}" class="btn btn-sm btn-outline-primary w-100 mt-2">
                                    View All Templates
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Step 4: Assessments -->
            {% if current_step == 4 %}
            <div id="step4" class="wizard-step">
                <h2 class="mb-4">Assessments</h2>
                <p class="text-muted mb-4">Create quizzes and assignments to test student knowledge.</p>
                
                <div id="assessmentCreation">
                    <!-- Assessment creation interface will be loaded here -->
                </div>
            </div>
            {% endif %}

            <!-- Step 5: Settings & Publishing -->
            {% if current_step == 5 %}
            <div id="step5" class="wizard-step">
                <h2 class="mb-4">Settings & Publishing</h2>
                <p class="text-muted mb-4">Configure final settings and publish your course.</p>
                
                <form id="publishingForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="coursePrice" class="form-label">Course Price ($)</label>
                                <input type="number" class="form-control" id="coursePrice" name="price" value="0" min="0" step="0.01">
                                <small class="text-muted">Set to 0 for free course</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="enrollmentLimit" class="form-label">Enrollment Limit</label>
                                <input type="number" class="form-control" id="enrollmentLimit" name="enrollment_limit" placeholder="Leave empty for unlimited">
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="isFeatured" name="is_featured">
                                <label class="form-check-label" for="isFeatured">
                                    Feature this course on homepage
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Course Preview</h5>
                            <div class="border rounded p-3 bg-light">
                                <h6 id="previewTitle">Course Title</h6>
                                <p id="previewDescription" class="text-muted small">Course description will appear here...</p>
                                <div class="d-flex justify-content-between">
                                    <span id="previewPrice" class="fw-bold text-success">Free</span>
                                    <span id="previewDuration" class="text-muted">10 hours</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            {% endif %}
        </div>

        <!-- Navigation -->
        <div class="wizard-navigation">
            <div class="container">
                <div class="d-flex justify-content-between">
                    <div>
                        {% if current_step > 1 %}
                        <button type="button" class="btn btn-outline-secondary" onclick="previousStep()">
                            <i class="fas fa-arrow-left me-2"></i>
                            Previous
                        </button>
                        {% endif %}
                    </div>
                    
                    <div>
                        <button type="button" class="btn btn-outline-primary me-2" onclick="saveDraft()">
                            <i class="fas fa-save me-2"></i>
                            Save Draft
                        </button>
                        
                        {% if current_step < 5 %}
                        <button type="button" class="btn btn-primary" onclick="nextStep()">
                            Next
                            <i class="fas fa-arrow-right ms-2"></i>
                        </button>
                        {% else %}
                        <button type="button" class="btn btn-success" onclick="publishCourse()">
                            <i class="fas fa-rocket me-2"></i>
                            Publish Course
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for session data -->
<form id="sessionForm" style="display: none;">
    {% csrf_token %}
    <input type="hidden" id="sessionId" name="session_id" value="{{ course_session.id|default:'' }}">
    <input type="hidden" id="courseId" name="course_id" value="{{ course_session.course.id|default:'' }}">
    <input type="hidden" id="currentStep" name="current_step" value="{{ current_step }}">
</form>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script src="https://cdn.tiny.cloud/1/{{ settings.TINYMCE_API_KEY }}/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/course-builder.js' %}"></script>
<script>
// Initialize TinyMCE when on step 3
if ({{ current_step }} === 3) {
    tinymce.init({
        selector: 'textarea.tinymce',
        height: 500,
        menubar: true,
        branding: false,
        promotion: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons',
            'template', 'codesample', 'hr', 'pagebreak', 'nonbreaking',
            'toc', 'imagetools', 'textpattern', 'noneditable', 'quickbars'
        ],
        toolbar1: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat',
        toolbar2: 'forecolor backcolor | insertdatetime | codesample | hr pagebreak | template | fullscreen preview | help',
        quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
        quickbars_insert_toolbar: 'quickimage quicktable',
        contextmenu: 'link image table',
        skin: 'oxide',
        content_css: 'default',
        content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif; font-size: 16px; line-height: 1.6; color: #333; max-width: 100%; margin: 0 auto; padding: 1rem; } h1, h2, h3, h4, h5, h6 { color: #1a2e53; margin-top: 1.5em; margin-bottom: 0.5em; } a { color: #ff5d15; } blockquote { border-left: 4px solid #ff5d15; padding-left: 1rem; margin-left: 0; font-style: italic; } img { max-width: 100%; height: auto; border-radius: 8px; }',
        image_advtab: true,
        file_picker_types: 'image',
        file_picker_callback: function (callback, value, meta) {
            if (meta.filetype === 'image') {
                const mediaWindow = window.open('/course-builder/media/', 'mediaLibrary', 'width=1000,height=700,scrollbars=yes');
                window.insertMediaIntoEditor = function(url, type) {
                    callback(url, { alt: 'Course Image' });
                    mediaWindow.close();
                };
            }
        },
        templates: [
            {
                title: 'Learning Objective',
                description: 'Template for learning objectives',
                content: '<h3>Learning Objective</h3><p><strong>By the end of this lesson, you will be able to:</strong></p><ul><li>Objective 1</li><li>Objective 2</li><li>Objective 3</li></ul>'
            },
            {
                title: 'Key Takeaway',
                description: 'Highlight important information',
                content: '<div style="background: #fff8e1; border-left: 4px solid #ff5d15; padding: 1rem; margin: 1rem 0;"><h4 style="color: #ff5d15; margin-top: 0;">💡 Key Takeaway</h4><p>Your important message here...</p></div>'
            },
            {
                title: 'Exercise',
                description: 'Template for exercises and activities',
                content: '<div style="background: #f0f8ff; border: 2px solid #1a2e53; border-radius: 8px; padding: 1rem; margin: 1rem 0;"><h4 style="color: #1a2e53; margin-top: 0;">🎯 Exercise</h4><p><strong>Instructions:</strong></p><ol><li>Step 1</li><li>Step 2</li><li>Step 3</li></ol></div>'
            }
        ],
        setup: function (editor) {
            editor.on('change', function () {
                if (window.courseBuilder) {
                    clearTimeout(window.courseBuilder.contentSaveTimeout);
                    window.courseBuilder.contentSaveTimeout = setTimeout(() => {
                        window.courseBuilder.saveSession();
                    }, 3000);
                }
            });
        }
    });
}

// Media Library and Content Functions
function openMediaLibrary() {
    window.open('{% url "course_builder:media_library" %}', 'mediaLibrary', 'width=800,height=600');
}

function insertContentBlock() {
    // Implementation for inserting content blocks
    console.log('Insert content block');
}

function saveLessonContent() {
    const lessonId = document.getElementById('lessonSelector').value;
    if (!lessonId) {
        alert('Please select a lesson first');
        return;
    }

    const content = tinymce.get('lessonContent').getContent();
    const videoUrl = document.getElementById('videoUrl').value;
    const duration = document.getElementById('lessonDuration').value;

    // Save via AJAX
    const formData = new FormData();
    formData.append('action', 'add_content');
    formData.append('lesson_id', lessonId);
    formData.append('content', content);
    formData.append('video_url', videoUrl);
    formData.append('duration', duration);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch('/course-builder/api/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Content saved successfully!');
        } else {
            alert('Error: ' + data.error);
        }
    });
}

function loadLessonContent() {
    const lessonId = document.getElementById('lessonSelector').value;
    if (lessonId) {
        document.getElementById('lessonContentEditor').style.display = 'block';
        // Load existing content if available
    } else {
        document.getElementById('lessonContentEditor').style.display = 'none';
    }
}
</script>
{% endblock %}
