{% extends 'lms/base.html' %}
{% load static %}

{% block title %}Interactive Exercises - YITP LMS{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-puzzle-piece me-2"></i>Interactive Exercises
                </h1>
                <a href="{% url 'content:library' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Library
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        {% for exercise in exercises %}
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">{{ exercise.title }}</h5>
                    <p class="card-text">{{ exercise.description|truncatewords:20 }}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">{{ exercise.get_exercise_type_display }}</small>
                        <span class="badge bg-warning">{{ exercise.difficulty_level|title }}</span>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'content:exercise_detail' exercise.id %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-play me-1"></i>Start Exercise
                    </a>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-puzzle-piece fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No exercises available</h4>
                <p class="text-muted">Interactive exercises will appear here when they are added.</p>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}