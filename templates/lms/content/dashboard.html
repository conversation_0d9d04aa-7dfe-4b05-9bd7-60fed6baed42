{% extends 'lms/base.html' %}
{% load static %}

{% block title %}Content Dashboard - YITP LMS{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-book-open me-2"></i>
                    Content Library Dashboard
                </h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'content:library' %}" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Browse Library
                    </a>
                    <a href="{% url 'content:search' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-search me-1"></i>Search Content
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_resources }}</h4>
                            <p class="card-text">Resources</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_content_items }}</h4>
                            <p class="card-text">Content Items</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_exercises }}</h4>
                            <p class="card-text">Exercises</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-puzzle-piece fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ recent_resources|length }}</h4>
                            <p class="card-text">Recent Items</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Resources -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Recent Resources
                    </h5>
                    <a href="{% url 'content:resource_list' %}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_resources %}
                        <div class="list-group list-group-flush">
                            {% for resource in recent_resources %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">{{ resource.title }}</div>
                                    <p class="mb-1 text-muted">{{ resource.description|truncatewords:15 }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-tag me-1"></i>{{ resource.get_resource_type_display }}
                                        <i class="fas fa-download ms-2 me-1"></i>{{ resource.download_count }} downloads
                                    </small>
                                </div>
                                <span class="badge bg-primary rounded-pill">{{ resource.access_level|title }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-file-alt fa-3x mb-3"></i>
                            <p>No resources available</p>
                            <a href="{% url 'content:library' %}" class="btn btn-primary">Browse Library</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Popular Resources -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Popular Resources
                    </h5>
                    <a href="{% url 'content:resource_list' %}?sort=popular" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if popular_resources %}
                        <div class="list-group list-group-flush">
                            {% for resource in popular_resources %}
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ resource.title }}</h6>
                                    <small class="text-muted">{{ resource.download_count }} downloads</small>
                                </div>
                                <p class="mb-1">{{ resource.description|truncatewords:12 }}</p>
                                <small class="text-muted">
                                    <i class="fas fa-tag me-1"></i>{{ resource.get_resource_type_display }}
                                    {% if resource.category %}
                                    <i class="fas fa-folder ms-2 me-1"></i>{{ resource.category|title }}
                                    {% endif %}
                                </small>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-star fa-3x mb-3"></i>
                            <p>No popular resources yet</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Content Categories -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-th-large me-2"></i>Content Categories
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{% url 'content:resource_list' %}?type=document" class="btn btn-outline-primary w-100 mb-3">
                                <i class="fas fa-file-pdf fa-2x d-block mb-2"></i>
                                <span>Documents</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'content:resource_list' %}?type=template" class="btn btn-outline-success w-100 mb-3">
                                <i class="fas fa-file-contract fa-2x d-block mb-2"></i>
                                <span>Templates</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'content:resource_list' %}?type=video" class="btn btn-outline-info w-100 mb-3">
                                <i class="fas fa-video fa-2x d-block mb-2"></i>
                                <span>Videos</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'content:exercise_list' %}" class="btn btn-outline-warning w-100 mb-3">
                                <i class="fas fa-puzzle-piece fa-2x d-block mb-2"></i>
                                <span>Exercises</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{% url 'content:library' %}" class="btn btn-outline-primary w-100 mb-2">
                                <i class="fas fa-book-open me-2"></i>Browse Library
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'content:search' %}" class="btn btn-outline-success w-100 mb-2">
                                <i class="fas fa-search me-2"></i>Search Content
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'content:resource_list' %}" class="btn btn-outline-info w-100 mb-2">
                                <i class="fas fa-list me-2"></i>All Resources
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'content:exercise_list' %}" class="btn btn-outline-warning w-100 mb-2">
                                <i class="fas fa-puzzle-piece me-2"></i>Interactive Exercises
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #ff5d15 0%, #ff7b3d 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
}

.btn-primary {
    background: linear-gradient(135deg, #ff5d15 0%, #ff7b3d 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #e54d0f 0%, #e56b2d 100%);
    transform: translateY(-1px);
}

.text-primary {
    color: #ff5d15 !important;
}

.bg-primary {
    background: linear-gradient(135deg, #ff5d15 0%, #ff7b3d 100%) !important;
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}
</style>
{% endblock %}
