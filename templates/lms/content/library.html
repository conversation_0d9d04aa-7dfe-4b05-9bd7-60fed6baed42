{% extends 'lms/base.html' %}
{% load static %}

{% block title %}Content Library - YITP LMS{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-book-open me-2"></i>Content Library
                </h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'content:search' %}" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Search Content
                    </a>
                    <a href="{% url 'content:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Categories -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-file-pdf fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Resources</h5>
                    <p class="card-text">{{ resources|length }} items</p>
                    <a href="{% url 'content:resource_list' %}" class="btn btn-primary">Browse</a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-layer-group fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Content Items</h5>
                    <p class="card-text">{{ content_items|length }} items</p>
                    <a href="{% url 'content:content_item_list' %}" class="btn btn-success">Browse</a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-puzzle-piece fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">Exercises</h5>
                    <p class="card-text">{{ exercises|length }} items</p>
                    <a href="{% url 'content:exercise_list' %}" class="btn btn-warning">Browse</a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-folder fa-3x text-info mb-3"></i>
                    <h5 class="card-title">Libraries</h5>
                    <p class="card-text">{{ libraries|length }} collections</p>
                    <a href="#libraries" class="btn btn-info">Browse</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Resources -->
    <div class="row">
        <div class="col-12">
            <h4 class="mb-3">Featured Resources</h4>
            <div class="row">
                {% for resource in resources %}
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">{{ resource.title }}</h5>
                            <p class="card-text">{{ resource.description|truncatewords:20 }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">{{ resource.get_resource_type_display }}</small>
                                <span class="badge bg-primary">{{ resource.access_level|title }}</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <a href="{% url 'content:resource_detail' resource.id %}" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View
                            </a>
                            {% if resource.file_path %}
                            <a href="{{ resource.file_path.url }}" class="btn btn-outline-success btn-sm" download>
                                <i class="fas fa-download me-1"></i>Download
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}