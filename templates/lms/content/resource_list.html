{% extends 'lms/base.html' %}
{% load static %}

{% block title %}Resources - YITP LMS{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-file-alt me-2"></i>Resources
                </h1>
                <a href="{% url 'content:library' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Library
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        {% for resource in resources %}
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">{{ resource.title }}</h5>
                    <p class="card-text">{{ resource.description|truncatewords:20 }}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">{{ resource.get_resource_type_display }}</small>
                        <span class="badge bg-primary">{{ resource.access_level|title }}</span>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'content:resource_detail' resource.id %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>View
                    </a>
                    {% if resource.file_path %}
                    <a href="{{ resource.file_path.url }}" class="btn btn-outline-success btn-sm" download>
                        <i class="fas fa-download me-1"></i>Download
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No resources available</h4>
                <p class="text-muted">Resources will appear here when they are added.</p>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}