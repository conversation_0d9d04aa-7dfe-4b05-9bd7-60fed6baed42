{% extends 'lms/base.html' %}
{% load static %}

{% block title %}Content Items - YITP LMS{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-layer-group me-2"></i>Content Items
                </h1>
                <a href="{% url 'content:library' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Library
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        {% for item in content_items %}
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">{{ item.title }}</h5>
                    <p class="card-text">{{ item.content|truncatewords:30|striptags }}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">{{ item.get_content_type_display }}</small>
                        <span class="badge bg-success">{{ item.difficulty_level|title }}</span>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'content:content_item_detail' item.id %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>View Content
                    </a>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-layer-group fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No content items available</h4>
                <p class="text-muted">Content items will appear here when they are added.</p>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}