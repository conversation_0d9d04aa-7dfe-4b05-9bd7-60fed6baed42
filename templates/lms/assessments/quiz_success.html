{% extends 'lms/base.html' %}
{% load static %}

{% block title %}Quiz Completed Successfully! - YITP LMS{% endblock %}

{% block extra_css %}
<style>
    .content-wrapper {
        margin-top: 100px;
    }
    
    @media (max-width: 991px) {
        .content-wrapper {
            margin-top: 90px;
        }
    }
    
    @media (max-width: 575px) {
        .content-wrapper {
            margin-top: 80px;
        }
    }
    
    .success-header {
        background: linear-gradient(135deg, #ff5d15, #1a2e53);
        color: white;
        border-radius: 20px;
        padding: 3rem 2rem;
        text-align: center;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .success-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }
    
    .success-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        animation: bounce 1s ease-in-out;
    }
    
    .score-display {
        background: rgba(255,255,255,0.2);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 2rem auto;
        max-width: 300px;
        backdrop-filter: blur(10px);
    }
    
    .score-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: conic-gradient(#28a745 0deg, #28a745 var(--score-angle), rgba(255,255,255,0.3) var(--score-angle));
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        position: relative;
    }
    
    .score-circle::before {
        content: '';
        position: absolute;
        width: 90px;
        height: 90px;
        background: white;
        border-radius: 50%;
    }
    
    .score-text {
        position: relative;
        z-index: 1;
        font-size: 1.5rem;
        font-weight: bold;
        color: #1a2e53;
    }
    
    .achievements-section {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .achievement-item {
        background: linear-gradient(135deg, rgba(255, 93, 21, 0.1), rgba(26, 46, 83, 0.1));
        border: 2px solid #ff5d15;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        animation: achievementSlideIn 0.8s ease-out;
        transition: transform 0.3s ease;
    }
    
    .achievement-item:hover {
        transform: translateY(-3px);
    }
    
    .achievement-icon {
        font-size: 2.5rem;
        color: #ff5d15;
        margin-right: 1rem;
    }
    
    .points-earned {
        background: linear-gradient(135deg, #ff5d15, #1a2e53);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: bold;
        display: inline-block;
        margin-top: 0.5rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-icon {
        font-size: 2rem;
        color: #ff5d15;
        margin-bottom: 0.5rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #1a2e53;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 2rem;
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, #ff5d15, #e54d0f);
        border: none;
        border-radius: 25px;
        padding: 1rem 2rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 93, 21, 0.3);
    }
    
    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 93, 21, 0.4);
        color: white;
    }
    
    .btn-secondary-custom {
        background: transparent;
        border: 2px solid #1a2e53;
        border-radius: 25px;
        padding: 1rem 2rem;
        color: #1a2e53;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .btn-secondary-custom:hover {
        background: #1a2e53;
        color: white;
        transform: translateY(-2px);
    }
    
    .progress-indicator {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .progress-bar-custom {
        height: 12px;
        border-radius: 6px;
        background: #e9ecef;
        overflow: hidden;
        margin-top: 0.5rem;
    }
    
    .progress-fill-custom {
        height: 100%;
        background: linear-gradient(135deg, #ff5d15, #1a2e53);
        border-radius: 6px;
        transition: width 2s ease-in-out;
        animation: progressFill 2s ease-in-out;
    }
    
    /* Animations */
    @keyframes shimmer {
        0%, 100% { transform: translateX(-100%) translateY(-100%); }
        50% { transform: translateX(0%) translateY(0%); }
    }
    
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-20px); }
        60% { transform: translateY(-10px); }
    }
    
    @keyframes achievementSlideIn {
        0% {
            transform: translateX(-100%);
            opacity: 0;
        }
        100% {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes progressFill {
        0% { width: 0%; }
    }
    
    .celebration-confetti {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1000;
    }
    
    .confetti-piece {
        position: absolute;
        width: 10px;
        height: 10px;
        background: #ff5d15;
        animation: confettiFall 3s linear infinite;
    }
    
    @keyframes confettiFall {
        0% {
            transform: translateY(-100vh) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }
    
    @media (max-width: 768px) {
        .success-header {
            padding: 2rem 1rem;
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .btn-primary-custom,
        .btn-secondary-custom {
            width: 100%;
            max-width: 300px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <div class="container-fluid py-4">
        <!-- Success Header -->
        <div class="success-header">
            <div class="success-icon">
                🎉
            </div>
            <h1 class="mb-3">Congratulations!</h1>
            <h4 class="mb-4">You've successfully passed the quiz!</h4>
            
            <!-- Score Display -->
            <div class="score-display">
                <div class="score-circle" style="--score-angle: {{ score_percentage|floatformat:0 }}deg;">
                    <div class="score-text">{{ attempt.score|floatformat:1 }}%</div>
                </div>
                <p class="mb-0">
                    <strong>Passing Score:</strong> {{ quiz.passing_score }}%
                </p>
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-value">+{{ points_awarded }}</div>
                <div class="stat-label">Points Earned</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-fire"></i>
                </div>
                <div class="stat-value">{{ current_streak }}</div>
                <div class="stat-label">Learning Streak</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-value">{{ total_points }}</div>
                <div class="stat-label">Total Points</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="stat-value">{{ attempt.attempt_number }}</div>
                <div class="stat-label">Attempt #</div>
            </div>
        </div>

        <!-- New Achievements -->
        {% if new_achievements %}
        <div class="achievements-section">
            <h3 class="text-center mb-4">
                <i class="fas fa-trophy text-warning me-2"></i>
                New Achievements Unlocked!
            </h3>
            {% for achievement in new_achievements %}
            <div class="achievement-item">
                <div class="d-flex align-items-center">
                    <div class="achievement-icon">
                        <i class="{{ achievement.badge_icon }}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="mb-1">{{ achievement.title }}</h5>
                        <p class="mb-0 text-muted">{{ achievement.description }}</p>
                        <div class="points-earned">
                            +{{ achievement.points }} points
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Course Progress -->
        <div class="progress-indicator">
            <h5 class="mb-3">
                <i class="fas fa-chart-line me-2"></i>
                Course Progress
            </h5>
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span>{{ course_progress.completed_lessons }} of {{ course_progress.total_lessons }} lessons completed</span>
                <span class="fw-bold">{{ course_progress.percentage|floatformat:1 }}%</span>
            </div>
            <div class="progress-bar-custom">
                <div class="progress-fill-custom" style="width: {{ course_progress.percentage }}%;"></div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            {% if next_lesson_url %}
            <a href="{{ next_lesson_url }}" class="btn-primary-custom" role="button">
                <i class="fas fa-arrow-right me-2"></i>
                Continue to Next Lesson
            </a>
            {% else %}
            <div class="text-center">
                <h4 class="text-success mb-3">🎓 Course Completed!</h4>
                <a href="{% url 'courses:course_detail' slug=quiz.lesson.module.course.slug %}" class="btn-primary-custom" role="button">
                    <i class="fas fa-certificate me-2"></i>
                    View Course Summary
                </a>
            </div>
            {% endif %}
            
            <a href="{% url 'assessments:quiz_results' attempt_id=attempt.id %}" class="btn-secondary-custom" role="button">
                <i class="fas fa-chart-bar me-2"></i>
                Review Quiz Results
            </a>
            
            <a href="{% url 'progress:achievements' %}" class="btn-secondary-custom" role="button">
                <i class="fas fa-trophy me-2"></i>
                View All Achievements
            </a>
        </div>
    </div>
</div>

<!-- Celebration Confetti -->
<div class="celebration-confetti" id="confetti"></div>
{% endblock %}

{% block extra_js %}
<script>
// Create confetti animation
function createConfetti() {
    const confettiContainer = document.getElementById('confetti');
    const colors = ['#ff5d15', '#1a2e53', '#28a745', '#ffc107', '#dc3545'];
    
    for (let i = 0; i < 50; i++) {
        const confetti = document.createElement('div');
        confetti.className = 'confetti-piece';
        confetti.style.left = Math.random() * 100 + '%';
        confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
        confetti.style.animationDelay = Math.random() * 3 + 's';
        confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
        confettiContainer.appendChild(confetti);
    }
    
    // Remove confetti after animation
    setTimeout(() => {
        confettiContainer.innerHTML = '';
    }, 6000);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Start confetti animation
    createConfetti();
    
    // Animate achievement cards
    const achievementItems = document.querySelectorAll('.achievement-item');
    achievementItems.forEach((item, index) => {
        item.style.animationDelay = (index * 0.2) + 's';
    });
    
    // Animate stats cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, (index * 100) + 500);
    });
    
    // Play success sound (if available)
    try {
        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
        audio.play().catch(() => {}); // Ignore errors if audio fails
    } catch (e) {}
});

// Add keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' || e.key === ' ') {
        const nextButton = document.querySelector('.btn-primary-custom');
        if (nextButton && document.activeElement === nextButton) {
            nextButton.click();
        }
    }
});
</script>
{% endblock %}
