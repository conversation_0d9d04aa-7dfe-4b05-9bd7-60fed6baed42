{% extends 'lms/base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Compose Message - YITP LMS{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Compose New Message
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {{ form|crispy }}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'communication:inbox' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}