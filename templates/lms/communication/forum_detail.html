{% extends 'lms/base.html' %}
{% load static %}

{% block title %}{{ forum.title }} - YITP LMS{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 text-primary">{{ forum.title }}</h1>
                    <p class="text-muted mb-0">{{ forum.description }}</p>
                </div>
                <div class="btn-group" role="group">
                    <a href="{% url 'communication:create_topic' forum.id %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>New Topic
                    </a>
                    <a href="{% url 'communication:forum_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Forums
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-comments me-2"></i>Topics
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if topics %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Topic</th>
                                        <th width="15%">Author</th>
                                        <th width="10%">Replies</th>
                                        <th width="15%">Last Activity</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for topic in topics %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% if topic.is_pinned %}
                                                <i class="fas fa-thumbtack text-warning me-2"></i>
                                                {% endif %}
                                                <div>
                                                    <a href="{% url 'communication:topic_detail' topic.id %}" class="fw-bold text-decoration-none">
                                                        {{ topic.title }}
                                                    </a>
                                                    {% if topic.is_locked %}
                                                    <i class="fas fa-lock text-muted ms-1"></i>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ topic.created_by.get_full_name|default:topic.created_by.username }}</td>
                                        <td>{{ topic.reply_count }}</td>
                                        <td>
                                            <small class="text-muted">{{ topic.last_activity|timesince }} ago</small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">No topics yet</h4>
                            <p class="text-muted">Be the first to start a discussion!</p>
                            <a href="{% url 'communication:create_topic' forum.id %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Create First Topic
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}