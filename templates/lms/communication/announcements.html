{% extends 'lms/base.html' %}
{% load static %}

{% block title %}Announcements - YITP LMS{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-bullhorn me-2"></i>Announcements
                </h1>
                <a href="{% url 'communication:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            {% for announcement in announcements %}
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ announcement.title }}</h5>
                    <div>
                        {% if announcement.priority == 'urgent' %}
                        <span class="badge bg-danger">Urgent</span>
                        {% elif announcement.priority == 'high' %}
                        <span class="badge bg-warning">High Priority</span>
                        {% endif %}
                        <small class="text-muted ms-2">{{ announcement.publish_date|date:"M d, Y" }}</small>
                    </div>
                </div>
                <div class="card-body">
                    <p class="card-text">{{ announcement.content|linebreaks }}</p>
                    <small class="text-muted">
                        <i class="fas fa-book me-1"></i>{{ announcement.course.title }}
                        <i class="fas fa-user ms-3 me-1"></i>{{ announcement.created_by.get_full_name|default:announcement.created_by.username }}
                    </small>
                </div>
            </div>
            {% empty %}
            <div class="text-center py-5">
                <i class="fas fa-bullhorn fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No announcements</h4>
                <p class="text-muted">Announcements will appear here when they are posted.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}