{% extends 'lms/base.html' %}
{% load static %}

{% block title %}Inbox - YITP LMS{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-inbox me-2"></i>
                    Inbox
                </h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'communication:compose' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>New Message
                    </a>
                    <a href="{% url 'communication:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-envelope me-2"></i>Messages
                        </h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <input type="radio" class="btn-check" name="filter" id="all" autocomplete="off" checked>
                            <label class="btn btn-outline-primary" for="all">All</label>

                            <input type="radio" class="btn-check" name="filter" id="unread" autocomplete="off">
                            <label class="btn btn-outline-primary" for="unread">Unread</label>

                            <input type="radio" class="btn-check" name="filter" id="sent" autocomplete="off">
                            <label class="btn btn-outline-primary" for="sent">Sent</label>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if messages %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </th>
                                        <th width="20%">From/To</th>
                                        <th width="40%">Subject</th>
                                        <th width="20%">Date</th>
                                        <th width="15%">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for message in messages %}
                                    <tr class="message-row {% if not message.is_read %}table-primary{% endif %}" data-message-id="{{ message.id }}">
                                        <td>
                                            <input type="checkbox" class="form-check-input message-checkbox" value="{{ message.id }}">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        {{ message.sender.first_name|first|default:message.sender.username|first|upper }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ message.sender.get_full_name|default:message.sender.username }}</div>
                                                    <small class="text-muted">{{ message.sender.email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="message-subject">
                                                {% if not message.is_read %}
                                                <i class="fas fa-circle text-primary me-1" style="font-size: 0.5rem;"></i>
                                                {% endif %}
                                                <strong>{{ message.subject }}</strong>
                                                <br>
                                                <small class="text-muted">{{ message.content|truncatewords:10 }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ message.sent_at|date:"M d, Y" }}</span>
                                            <br>
                                            <small class="text-muted">{{ message.sent_at|time:"g:i A" }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'communication:message_detail' message.id %}" class="btn btn-outline-primary" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'communication:reply' message.id %}" class="btn btn-outline-success" title="Reply">
                                                    <i class="fas fa-reply"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-danger delete-message" data-message-id="{{ message.id }}" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                        <div class="card-footer">
                            <nav aria-label="Messages pagination">
                                <ul class="pagination justify-content-center mb-0">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">Your inbox is empty</h4>
                            <p class="text-muted">No messages to display</p>
                            <a href="{% url 'communication:compose' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Send Your First Message
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.message-row {
    cursor: pointer;
    transition: all 0.2s ease;
}

.message-row:hover {
    background-color: rgba(255, 93, 21, 0.1) !important;
}

.table-primary {
    background-color: rgba(255, 93, 21, 0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #ff5d15 0%, #ff7b3d 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #e54d0f 0%, #e56b2d 100%);
}

.text-primary {
    color: #ff5d15 !important;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background: linear-gradient(135deg, #ff5d15 0%, #ff7b3d 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all checkbox functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const messageCheckboxes = document.querySelectorAll('.message-checkbox');
    
    selectAllCheckbox.addEventListener('change', function() {
        messageCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Message row click to view
    document.querySelectorAll('.message-row').forEach(row => {
        row.addEventListener('click', function(e) {
            if (!e.target.closest('input') && !e.target.closest('button') && !e.target.closest('a')) {
                const messageId = this.dataset.messageId;
                window.location.href = `/lms/communication/messages/${messageId}/`;
            }
        });
    });
    
    // Delete message functionality
    document.querySelectorAll('.delete-message').forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            if (confirm('Are you sure you want to delete this message?')) {
                // Add delete functionality here
                console.log('Delete message:', this.dataset.messageId);
            }
        });
    });
});
</script>
{% endblock %}
