{% extends 'lms/base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Reply to Message - YITP LMS{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Original Message -->
            {% if original_message %}
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Original Message</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <strong>{{ original_message.subject }}</strong>
                        <small class="text-muted">{{ original_message.sent_at }}</small>
                    </div>
                    <p class="mb-2"><strong>From:</strong> {{ original_message.sender.get_full_name|default:original_message.sender.username }}</p>
                    <div class="border-start border-3 border-secondary ps-3">
                        {{ original_message.content|linebreaks }}
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card mb-4">
                <div class="card-body text-center">
                    <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                    <h5>Reply to Message</h5>
                    <p class="text-muted">Compose your reply below.</p>
                </div>
            </div>
            {% endif %}

            <!-- Reply Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-reply me-2"></i>Reply
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {{ form|crispy }}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'communication:inbox' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-reply me-1"></i>Send Reply
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}