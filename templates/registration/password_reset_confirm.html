{% extends 'yitp/base.html' %}
{% load static %}

{% block title %}Set New Password - Youth Impact Training Programme{% endblock %}

{% block extra_css %}
<style>
    /* YITP Password Reset Confirm Styles */
    .content-wrapper {
        margin-top: 100px; /* Account for fixed navbar */
        margin-bottom: 50px;
    }

    @media (max-width: 991px) {
        .content-wrapper {
            margin-top: 90px;
        }
    }

    @media (max-width: 575px) {
        .content-wrapper {
            margin-top: 80px;
        }
    }

    .auth-container {
        min-height: calc(100vh - 200px);
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 2rem 0;
    }
    
    .auth-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        max-width: 550px;
        width: 100%;
        margin: 0 auto;
    }
    
    .auth-header {
        background: linear-gradient(135deg, #ff5d15, #1a2e53);
        color: white;
        padding: 2.5rem 2rem;
        text-align: center;
    }
    
    .auth-header .logo {
        margin-bottom: 1rem;
    }
    
    .auth-header .logo img {
        height: 50px;
        width: auto;
    }
    
    .auth-header h1 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .auth-header p {
        margin: 0;
        opacity: 0.9;
        font-size: 1rem;
    }
    
    .auth-body {
        padding: 2.5rem 2rem;
    }
    
    .form-floating {
        margin-bottom: 1.5rem;
    }
    
    .form-floating .form-control {
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        padding: 1rem 0.75rem;
        height: auto;
        transition: all 0.3s ease;
        font-size: 1rem;
    }
    
    .form-floating .form-control:focus {
        border-color: #ff5d15;
        box-shadow: 0 0 0 0.2rem rgba(255, 93, 21, 0.25);
    }
    
    .form-floating label {
        color: #64748b;
        font-weight: 500;
    }
    
    .btn-yitp {
        background: linear-gradient(135deg, #ff5d15, #1a2e53);
        border: none;
        border-radius: 10px;
        padding: 0.875rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        width: 100%;
        color: white;
        font-size: 1rem;
    }
    
    .btn-yitp:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 93, 21, 0.4);
        color: white;
    }
    
    .auth-links {
        text-align: center;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e2e8f0;
    }
    
    .auth-links a {
        color: #ff5d15;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;
    }
    
    .auth-links a:hover {
        color: #1a2e53;
        text-decoration: underline;
    }
    
    .alert {
        border-radius: 10px;
        border: none;
        margin-bottom: 1.5rem;
    }
    
    .password-requirements {
        background: #f0f9ff;
        border: 2px solid #bae6fd;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        color: #0c4a6e;
    }
    
    .password-requirements i {
        color: #0284c7;
        margin-right: 0.5rem;
    }
    
    .password-requirements h5 {
        color: #0c4a6e;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .password-requirements ul {
        margin-bottom: 0;
        padding-left: 1.5rem;
    }
    
    .password-requirements li {
        margin-bottom: 0.25rem;
    }

    .error-message {
        background: #fee;
        border: 2px solid #fecaca;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        color: #991b1b;
        text-align: center;
    }
    
    .error-message i {
        color: #dc2626;
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .error-message h4 {
        color: #991b1b;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    /* YITP Brand Colors */
    .text-yitp-primary {
        color: #ff5d15 !important;
    }
    
    .text-yitp-secondary {
        color: #1a2e53 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="auth-card">
                        {% if validlink %}
                            <div class="auth-header">
                                <div class="logo">
                                    <img src="{% static 'img/logo/logo.png' %}" alt="YITP Logo">
                                </div>
                                <h1>Set New Password</h1>
                                <p>Youth Impact Training Programme</p>
                            </div>
                            
                            <div class="auth-body">
                                <div class="password-requirements">
                                    <i class="fas fa-shield-alt"></i>
                                    <h5>Password Requirements</h5>
                                    <ul>
                                        <li>At least 8 characters long</li>
                                        <li>Include uppercase and lowercase letters</li>
                                        <li>Include at least one number</li>
                                        <li>Include at least one special character (!@#$%^&*)</li>
                                        <li>Avoid common words or personal information</li>
                                    </ul>
                                </div>
                                
                                {% if form.errors %}
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        {% for field, errors in form.errors.items %}
                                            {% for error in errors %}
                                                {{ error }}<br>
                                            {% endfor %}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                
                                <form method="post">
                                    {% csrf_token %}
                                    
                                    <div class="form-floating">
                                        <input type="password" 
                                               class="form-control{% if form.new_password1.errors %} is-invalid{% endif %}" 
                                               id="id_new_password1" 
                                               name="new_password1" 
                                               placeholder="New password"
                                               required>
                                        <label for="id_new_password1">
                                            <i class="fas fa-lock me-2"></i>New Password
                                        </label>
                                        {% if form.new_password1.errors %}
                                            <div class="invalid-feedback">
                                                {{ form.new_password1.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="form-floating">
                                        <input type="password" 
                                               class="form-control{% if form.new_password2.errors %} is-invalid{% endif %}" 
                                               id="id_new_password2" 
                                               name="new_password2" 
                                               placeholder="Confirm new password"
                                               required>
                                        <label for="id_new_password2">
                                            <i class="fas fa-lock me-2"></i>Confirm New Password
                                        </label>
                                        {% if form.new_password2.errors %}
                                            <div class="invalid-feedback">
                                                {{ form.new_password2.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <button type="submit" class="btn btn-yitp">
                                        <i class="fas fa-check me-2"></i>
                                        Set New Password
                                    </button>
                                </form>
                                
                                <div class="auth-links">
                                    <p class="mb-0">
                                        <a href="{% url 'login' %}">
                                            <i class="fas fa-arrow-left me-1"></i>
                                            Back to Sign In
                                        </a>
                                    </p>
                                </div>
                            </div>
                        {% else %}
                            <div class="auth-header" style="background: linear-gradient(135deg, #dc3545, #c82333);">
                                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                <h1>Invalid Reset Link</h1>
                                <p>Youth Impact Training Programme</p>
                            </div>
                            
                            <div class="auth-body">
                                <div class="error-message">
                                    <i class="fas fa-times-circle"></i>
                                    <h4>Reset Link Expired or Invalid</h4>
                                    <p class="mb-0">This password reset link is no longer valid. It may have expired or already been used.</p>
                                </div>
                                
                                <div class="d-grid">
                                    <a href="{% url 'password_reset' %}" class="btn btn-yitp">
                                        <i class="fas fa-redo me-2"></i>
                                        Request New Reset Link
                                    </a>
                                </div>
                                
                                <div class="auth-links">
                                    <p class="mb-0">
                                        Need help? 
                                        <a href="mailto:<EMAIL>">
                                            <strong>Contact Support</strong>
                                        </a>
                                    </p>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
