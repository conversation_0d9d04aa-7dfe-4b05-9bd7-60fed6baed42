{% load i18n %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - Youth Impact Training Programme</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        /* Header */
        .email-header {
            background: linear-gradient(135deg, #ff5d15 0%, #1a2e53 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .logo {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .tagline {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 300;
        }
        
        /* Content */
        .email-content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .content-text {
            font-size: 16px;
            line-height: 1.8;
            color: #555555;
            margin-bottom: 20px;
        }
        
        /* Reset Button */
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #ff5d15, #1a2e53);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        
        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 93, 21, 0.4);
            color: white;
            text-decoration: none;
        }
        
        /* Info boxes */
        .info-box {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .warning-box {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        /* Footer */
        .email-footer {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 30px 20px;
            text-align: center;
            font-size: 14px;
        }
        
        .footer-links {
            margin: 20px 0;
        }
        
        .footer-links a {
            color: #ff5d15;
            text-decoration: none;
            margin: 0 15px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                margin: 0 !important;
            }
            
            .email-content {
                padding: 20px 15px !important;
            }
            
            .email-header {
                padding: 20px 15px !important;
            }
            
            .logo {
                font-size: 24px !important;
            }
            
            .reset-button {
                display: block !important;
                width: 100% !important;
                margin: 15px 0 !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="logo">Youth Impact Global</div>
            <div class="tagline">Training Programme for Future Leaders</div>
        </div>
        
        <!-- Content -->
        <div class="email-content">
            <div class="greeting">
                Hello {{ user.get_full_name|default:user.username }}! 🔐
            </div>
            
            <div class="content-text">
                You're receiving this email because you requested a password reset for your <strong>Youth Impact Training Programme</strong> account.
            </div>
            
            <div class="info-box">
                <strong>📧 Password Reset Request</strong><br>
                We received a request to reset the password for your account associated with this email address. If you made this request, please click the button below to set a new password.
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ protocol }}://{{ domain }}{% url 'password_reset_confirm' uidb64=uid token=token %}" class="reset-button">
                    🔑 Reset My Password
                </a>
            </div>
            
            <div class="warning-box">
                <strong>⚠️ Security Information:</strong><br>
                • This link will expire in <strong>24 hours</strong><br>
                • If you didn't request this reset, you can safely ignore this email<br>
                • Your password will remain unchanged until you create a new one<br>
                • For security, this link can only be used once
            </div>
            
            <div class="content-text">
                <strong>🔒 Password Security Tips:</strong>
            </div>
            
            <div class="content-text">
                • Use at least 8 characters with a mix of letters, numbers, and symbols<br>
                • Avoid using personal information or common words<br>
                • Don't reuse passwords from other accounts<br>
                • Consider using a password manager for better security
            </div>
            
            <div class="content-text">
                If you're having trouble clicking the button above, copy and paste the following link into your web browser:
            </div>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; word-break: break-all; font-family: monospace; font-size: 14px; color: #666;">
                {{ protocol }}://{{ domain }}{% url 'password_reset_confirm' uidb64=uid token=token %}
            </div>
            
            <div class="content-text" style="margin-top: 30px;">
                If you have any questions or concerns, please don't hesitate to contact our support team.
            </div>
            
            <div class="content-text">
                Best regards,<br>
                <strong>The YITP Security Team</strong><br>
                Youth Impact Training Programme
            </div>
        </div>
        
        <!-- Footer -->
        <div class="email-footer">
            <div class="footer-links">
                <a href="#">About Us</a>
                <a href="#">Programs</a>
                <a href="#">Contact</a>
                <a href="#">Support</a>
            </div>
            
            <p style="margin: 20px 0 10px 0;">
                <strong>Youth Impact Training Programme</strong><br>
                Empowering young minds for a better tomorrow
            </p>
            
            <p style="font-size: 12px; opacity: 0.8; margin: 10px 0;">
                If you have any questions, please contact us at 
                <a href="mailto:<EMAIL>" style="color: #ff5d15;">
                    <EMAIL>
                </a>
            </p>
            
            <p style="font-size: 11px; opacity: 0.6; margin-top: 20px;">
                This email was sent to {{ user.email }} because you requested a password reset.
                <br>© 2025 Youth Impact Global. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
