{% extends 'yitp/base.html' %}
{% load static %}

{% block title %}Password Reset Complete - Youth Impact Training Programme{% endblock %}

{% block extra_css %}
<style>
    /* YITP Password Reset Complete Styles */
    .content-wrapper {
        margin-top: 100px; /* Account for fixed navbar */
        margin-bottom: 50px;
    }

    @media (max-width: 991px) {
        .content-wrapper {
            margin-top: 90px;
        }
    }

    @media (max-width: 575px) {
        .content-wrapper {
            margin-top: 80px;
        }
    }

    .auth-container {
        min-height: calc(100vh - 200px);
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 2rem 0;
    }
    
    .auth-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        max-width: 550px;
        width: 100%;
        margin: 0 auto;
    }
    
    .auth-header {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 2.5rem 2rem;
        text-align: center;
    }
    
    .auth-header .success-icon {
        font-size: 3.5rem;
        margin-bottom: 1rem;
        opacity: 0.9;
    }
    
    .auth-header h1 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .auth-header p {
        margin: 0;
        opacity: 0.9;
        font-size: 1rem;
    }
    
    .auth-body {
        padding: 2.5rem 2rem;
        text-align: center;
    }
    
    .success-message {
        background: #d4edda;
        border: 2px solid #c3e6cb;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: #155724;
    }
    
    .success-message i {
        color: #28a745;
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    .success-message h4 {
        color: #155724;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .info-box {
        background: #f0f9ff;
        border: 2px solid #bae6fd;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        color: #0c4a6e;
        text-align: left;
    }
    
    .info-box i {
        color: #0284c7;
        margin-right: 0.5rem;
    }
    
    .info-box h5 {
        color: #0c4a6e;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .info-box ul {
        margin-bottom: 0;
        padding-left: 1.5rem;
    }
    
    .info-box li {
        margin-bottom: 0.5rem;
    }
    
    .btn-yitp {
        background: linear-gradient(135deg, #ff5d15, #1a2e53);
        border: none;
        border-radius: 10px;
        padding: 0.875rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        color: white;
        text-decoration: none;
        display: inline-block;
        margin: 0.5rem;
        font-size: 1rem;
    }
    
    .btn-yitp:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 93, 21, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .btn-outline-yitp {
        border: 2px solid #ff5d15;
        color: #ff5d15;
        background: transparent;
        border-radius: 10px;
        padding: 0.875rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin: 0.5rem;
    }
    
    .btn-outline-yitp:hover {
        background: #ff5d15;
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
    }
    
    .auth-links {
        text-align: center;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e2e8f0;
    }
    
    .auth-links a {
        color: #ff5d15;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;
    }
    
    .auth-links a:hover {
        color: #1a2e53;
        text-decoration: underline;
    }

    .security-tips {
        background: #fff3cd;
        border: 2px solid #ffeaa7;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        color: #856404;
        text-align: left;
    }
    
    .security-tips i {
        color: #f59e0b;
        margin-right: 0.5rem;
    }
    
    .security-tips h5 {
        color: #856404;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    /* YITP Brand Colors */
    .text-yitp-primary {
        color: #ff5d15 !important;
    }
    
    .text-yitp-secondary {
        color: #1a2e53 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-7">
                    <div class="auth-card">
                        <div class="auth-header">
                            <div class="success-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h1>Password Reset Complete!</h1>
                            <p>Youth Impact Training Programme</p>
                        </div>
                        
                        <div class="auth-body">
                            <div class="success-message">
                                <i class="fas fa-shield-check"></i>
                                <h4>Your Password Has Been Updated</h4>
                                <p class="mb-0">Your password has been successfully changed. You can now sign in with your new password.</p>
                            </div>
                            
                            <div class="security-tips">
                                <i class="fas fa-lightbulb"></i>
                                <h5>Security Tips</h5>
                                <ul>
                                    <li><strong>Keep your password secure</strong> - Don't share it with anyone</li>
                                    <li><strong>Use unique passwords</strong> - Don't reuse this password on other sites</li>
                                    <li><strong>Sign out on shared devices</strong> - Always log out when using public computers</li>
                                    <li><strong>Update regularly</strong> - Consider changing your password periodically</li>
                                </ul>
                            </div>
                            
                            <div class="info-box">
                                <i class="fas fa-info-circle"></i>
                                <h5>What's Next?</h5>
                                <ul>
                                    <li><strong>Sign in with your new password</strong> to access your account</li>
                                    <li><strong>Update your profile</strong> if needed</li>
                                    <li><strong>Continue your learning journey</strong> with YITP courses</li>
                                    <li><strong>Contact support</strong> if you experience any issues</li>
                                </ul>
                            </div>
                            
                            <div class="d-flex flex-wrap justify-content-center">
                                <a href="{% url 'login' %}" class="btn btn-yitp">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Sign In Now
                                </a>
                                <a href="{% url 'yitp:home' %}" class="btn btn-outline-yitp">
                                    <i class="fas fa-home me-2"></i>
                                    Go to Homepage
                                </a>
                            </div>
                            
                            <div class="auth-links">
                                <p class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        Your password was changed on {{ now|date:"F d, Y \a\t g:i A" }}
                                    </small>
                                </p>
                                <p class="mb-0">
                                    Need help? 
                                    <a href="mailto:<EMAIL>">
                                        <strong>Contact Support</strong>
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
