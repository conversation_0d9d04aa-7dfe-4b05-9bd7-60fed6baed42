{% extends 'yitp/base.html' %}
{% load static %}

{% block title %}Instructor Dashboard - YITP LMS{% endblock %}

{% block extra_css %}
<style>
    .content-wrapper {
        margin-top: 100px;
    }
    
    @media (max-width: 991px) {
        .content-wrapper {
            margin-top: 90px;
        }
    }
    
    @media (max-width: 575px) {
        .content-wrapper {
            margin-top: 80px;
        }
    }
    
    .instructor-header {
        background: linear-gradient(135deg, #ff5d15, #1a2e53);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        border-left: 5px solid #ff5d15;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-icon {
        font-size: 2.5rem;
        color: #ff5d15;
        margin-bottom: 0.5rem;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #1a2e53;
        margin-bottom: 0.25rem;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .dashboard-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #f8f9fa;
    }
    
    .section-icon {
        font-size: 1.5rem;
        color: #ff5d15;
        margin-right: 0.75rem;
    }
    
    .section-title {
        color: #1a2e53;
        margin: 0;
        font-weight: 600;
    }
    
    .course-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }
    
    .course-card:hover {
        border-color: #ff5d15;
        box-shadow: 0 4px 12px rgba(255, 93, 21, 0.1);
    }
    
    .course-title {
        color: #1a2e53;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .course-stats {
        display: flex;
        gap: 1rem;
        font-size: 0.85rem;
        color: #6c757d;
    }
    
    .message-item {
        border-left: 4px solid #ff5d15;
        padding: 1rem;
        margin-bottom: 1rem;
        background: #fff8f5;
        border-radius: 0 10px 10px 0;
    }
    
    .message-sender {
        font-weight: 600;
        color: #1a2e53;
        margin-bottom: 0.25rem;
    }
    
    .message-time {
        font-size: 0.8rem;
        color: #6c757d;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 2rem;
    }
    
    .action-btn {
        background: linear-gradient(135deg, #ff5d15, #e54d0f);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 1rem;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 93, 21, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .action-btn.secondary {
        background: linear-gradient(135deg, #1a2e53, #0f1a2e);
    }

    .action-btn.outline {
        background: transparent;
        color: #ff5d15;
        border: 2px solid #ff5d15;
    }

    .action-btn.outline:hover {
        background: #ff5d15;
        color: white;
    }
    
    .progress-bar-custom {
        height: 8px;
        border-radius: 4px;
        background: #e9ecef;
        overflow: hidden;
        margin-top: 0.5rem;
    }
    
    .progress-fill-custom {
        height: 100%;
        background: linear-gradient(135deg, #ff5d15, #1a2e53);
        border-radius: 4px;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active" aria-current="page">Dashboard</li>
{% endblock %}

{% block page_title_text %}
Welcome back, {{ instructor_profile.user.get_full_name }}!
{% endblock %}

{% block page_subtitle %}
<p class="page-subtitle text-muted mb-0">
    {{ instructor_profile.get_instructor_role_display }} Dashboard -
    <span class="badge bg-success">{{ instructor_profile.get_verification_status_display }}</span>
    <small class="ms-2">Last login: {{ user.last_login|date:"M d, Y H:i" }}</small>
</p>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    {% include 'instructor/navigation.html' %}

    <div class="container-fluid py-4">

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-value">{{ total_students }}</div>
                <div class="stat-label">Total Students</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-book"></i>
                </div>
                <div class="stat-value">{{ total_courses }}</div>
                <div class="stat-label">Total Courses</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="stat-value">{{ published_courses }}</div>
                <div class="stat-label">Published Courses</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="stat-value">{{ recent_enrollments }}</div>
                <div class="stat-label">New Enrollments (30d)</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value">{{ avg_quiz_score }}%</div>
                <div class="stat-label">Avg Quiz Score</div>
            </div>
        </div>

        <div class="row">
            <!-- Course Analytics -->
            <div class="col-lg-8">
                <div class="dashboard-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4 class="section-title">Course Performance</h4>
                    </div>
                    
                    {% if course_analytics %}
                        {% for analytics in course_analytics %}
                        <div class="course-card">
                            <div class="course-title">{{ analytics.course.title }}</div>
                            <div class="course-stats">
                                <span><i class="fas fa-users me-1"></i>{{ analytics.enrollments }} students</span>
                                <span><i class="fas fa-book-open me-1"></i>{{ analytics.total_lessons }} lessons</span>
                                <span><i class="fas fa-percentage me-1"></i>{{ analytics.completion_rate }}% completion</span>
                            </div>
                            <div class="progress-bar-custom">
                                <div class="progress-fill-custom" style="width: {{ analytics.completion_rate }}%;"></div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No course analytics available</h5>
                            <p class="text-muted">Create your first course to see performance data here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Messages -->
            <div class="col-lg-4">
                <div class="dashboard-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h4 class="section-title">Recent Messages</h4>
                    </div>
                    
                    {% if recent_messages %}
                        {% for message in recent_messages %}
                        <div class="message-item">
                            <div class="message-sender">{{ message.sender.get_full_name }}</div>
                            <div class="mb-2">{{ message.subject|truncatechars:50 }}</div>
                            <div class="message-time">{{ message.sent_at|timesince }} ago</div>
                        </div>
                        {% endfor %}
                        <div class="text-center mt-3">
                            <a href="{% url 'users:instructor_messages' %}" class="btn btn-outline-primary btn-sm">
                                View All Messages
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No recent messages</h6>
                            <p class="text-muted small">Student messages will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="{% url 'admin:courses_course_add' %}" class="action-btn">
                <i class="fas fa-plus me-2"></i>Create New Course
            </a>
            <a href="{% url 'users:instructor_courses' %}" class="action-btn secondary">
                <i class="fas fa-book me-2"></i>Manage Courses
            </a>
            <a href="{% url 'users:instructor_analytics' %}" class="action-btn">
                <i class="fas fa-chart-line me-2"></i>View Analytics
            </a>
            <a href="{% url 'users:instructor_messages' %}" class="action-btn secondary">
                <i class="fas fa-envelope me-2"></i>Messages
            </a>
            <a href="{% url 'users:instructor_tutorial' %}" class="action-btn outline">
                <i class="fas fa-graduation-cap me-2"></i>Tutorials
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add entrance animations
document.addEventListener('DOMContentLoaded', function() {
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Animate progress bars
    const progressBars = document.querySelectorAll('.progress-fill-custom');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
});
</script>
{% endblock %}
