{% extends "yitp/base.html" %}
{% load static %}

{% block title %}YITP Analytics Dashboard{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
<style>
    .analytics-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #ff5d15;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #ff5d15 0%, #ff7a3d 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .metric-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .chart-container {
        position: relative;
        height: 400px;
        margin: 1rem 0;
    }
    
    .analytics-nav {
        background: #1a2e53;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 2rem;
    }
    
    .analytics-nav a {
        color: white;
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        margin-right: 0.5rem;
        transition: background-color 0.3s;
    }
    
    .analytics-nav a:hover,
    .analytics-nav a.active {
        background-color: #ff5d15;
        color: white;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-success { background: #d4edda; color: #155724; }
    .status-warning { background: #fff3cd; color: #856404; }
    .status-danger { background: #f8d7da; color: #721c24; }
    .status-info { background: #d1ecf1; color: #0c5460; }
    
    .table-analytics {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .table-analytics th {
        background: #f8f9fa;
        border: none;
        font-weight: 600;
        color: #1a2e53;
    }
    
    .table-analytics td {
        border: none;
        border-bottom: 1px solid #e9ecef;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Analytics Navigation -->
    <div class="analytics-nav">
        <h4 class="text-white mb-3">📊 YITP Analytics Dashboard</h4>
        <nav>
            <a href="{% url 'analytics:dashboard' %}" class="{% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                🏠 Overview
            </a>
            <a href="{% url 'analytics:payment_methods' %}" class="{% if request.resolver_match.url_name == 'payment_methods' %}active{% endif %}">
                💳 Payment Methods
            </a>
            <a href="{% url 'analytics:user_behavior' %}" class="{% if request.resolver_match.url_name == 'user_behavior' %}active{% endif %}">
                👥 User Behavior
            </a>
            <a href="{% url 'analytics:course_revenue' %}" class="{% if request.resolver_match.url_name == 'course_revenue' %}active{% endif %}">
                📚 Course Revenue
            </a>
        </nav>
    </div>

    {% block analytics_content %}
    <!-- Content will be provided by child templates -->
    {% endblock %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
<script>
    // Common chart configuration
    Chart.defaults.font.family = "'Inter', sans-serif";
    Chart.defaults.color = '#6c757d';
    
    // YITP brand colors
    const YITP_COLORS = {
        primary: '#ff5d15',
        secondary: '#1a2e53',
        success: '#28a745',
        warning: '#ffc107',
        danger: '#dc3545',
        info: '#17a2b8',
        light: '#f8f9fa',
        dark: '#343a40'
    };
    
    // Utility functions
    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-KE', {
            style: 'currency',
            currency: 'KES',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }
    
    function formatNumber(number) {
        return new Intl.NumberFormat('en-US').format(number);
    }
    
    function formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
        });
    }
</script>
{% block analytics_js %}
<!-- Analytics-specific JavaScript will be provided by child templates -->
{% endblock %}
{% endblock %}
