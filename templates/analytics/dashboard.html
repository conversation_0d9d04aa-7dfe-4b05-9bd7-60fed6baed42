{% extends "analytics/base.html" %}
{% load static %}

{% block analytics_content %}
<!-- Key Metrics Row -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value">{{ summary.today.payments }}</div>
            <div class="metric-label">Today's Payments</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value">KES {{ summary.today.revenue|floatformat:0 }}</div>
            <div class="metric-label">Today's Revenue</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value">{{ summary.active_users }}</div>
            <div class="metric-label">Active Users</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value">{{ summary.pending_verifications }}</div>
            <div class="metric-label">Pending Verifications</div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <!-- Revenue Trends Chart -->
    <div class="col-lg-8">
        <div class="analytics-card">
            <h5 class="mb-3">📈 Revenue Trends (Last 30 Days)</h5>
            <div class="chart-container">
                <canvas id="revenueTrendsChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Payment Methods Pie Chart -->
    <div class="col-lg-4">
        <div class="analytics-card">
            <h5 class="mb-3">💳 Payment Methods</h5>
            <div class="chart-container">
                <canvas id="paymentMethodsChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Payment Method Performance -->
<div class="row">
    <div class="col-lg-6">
        <div class="analytics-card">
            <h5 class="mb-3">🎯 Payment Method Performance</h5>
            <div class="table-responsive">
                <table class="table table-analytics">
                    <thead>
                        <tr>
                            <th>Method</th>
                            <th>Count</th>
                            <th>Revenue</th>
                            <th>Success Rate</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <span class="badge bg-success">M-Pesa</span>
                            </td>
                            <td>{{ payment_methods.method_breakdown.mpesa.count }}</td>
                            <td>KES {{ payment_methods.method_breakdown.mpesa.revenue|floatformat:0 }}</td>
                            <td>
                                <span class="status-badge status-success">
                                    {{ payment_methods.method_breakdown.mpesa.success_rate|floatformat:1 }}%
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span class="badge bg-primary">Bank Transfer</span>
                            </td>
                            <td>{{ payment_methods.method_breakdown.bank_transfer.count }}</td>
                            <td>KES {{ payment_methods.method_breakdown.bank_transfer.revenue|floatformat:0 }}</td>
                            <td>
                                <span class="status-badge status-info">
                                    {{ payment_methods.method_breakdown.bank_transfer.success_rate|floatformat:1 }}%
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span class="badge bg-warning">PayPal</span>
                            </td>
                            <td>{{ payment_methods.method_breakdown.paypal.count }}</td>
                            <td>KES {{ payment_methods.method_breakdown.paypal.revenue|floatformat:0 }}</td>
                            <td>
                                <span class="status-badge status-warning">
                                    {{ payment_methods.method_breakdown.paypal.success_rate|floatformat:1 }}%
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Installment Analytics -->
    <div class="col-lg-6">
        <div class="analytics-card">
            <h5 class="mb-3">💰 Installment Analytics</h5>
            <div class="row">
                <div class="col-6">
                    <div class="text-center p-3">
                        <h3 class="text-primary">{{ installments.total_first_installments }}</h3>
                        <small class="text-muted">First Installments</small>
                    </div>
                </div>
                <div class="col-6">
                    <div class="text-center p-3">
                        <h3 class="text-success">{{ installments.total_second_installments }}</h3>
                        <small class="text-muted">Second Installments</small>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <h4 class="text-warning">{{ installments.completion_rate }}%</h4>
                <small class="text-muted">Completion Rate</small>
            </div>
            <div class="mt-3">
                <div class="d-flex justify-content-between">
                    <span>Active Partial Payments:</span>
                    <strong>{{ installments.active_partial_payments }}</strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>Expired Partial Payments:</span>
                    <strong>{{ installments.expired_partial_payments }}</strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>Installment Revenue:</span>
                    <strong>KES {{ installments.installment_revenue|floatformat:0 }}</strong>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Analytics -->
<div class="row">
    <div class="col-12">
        <div class="analytics-card">
            <h5 class="mb-3">📅 Recent Daily Analytics</h5>
            <div class="table-responsive">
                <table class="table table-analytics">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Total Payments</th>
                            <th>Revenue</th>
                            <th>Confirmed</th>
                            <th>Pending</th>
                            <th>Failed</th>
                            <th>Installments</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for analytics in recent_analytics %}
                        <tr>
                            <td>{{ analytics.date|date:"M d, Y" }}</td>
                            <td>{{ analytics.total_payments }}</td>
                            <td>KES {{ analytics.total_revenue|floatformat:0 }}</td>
                            <td>
                                <span class="status-badge status-success">{{ analytics.confirmed_payments }}</span>
                            </td>
                            <td>
                                <span class="status-badge status-warning">{{ analytics.pending_payments }}</span>
                            </td>
                            <td>
                                <span class="status-badge status-danger">{{ analytics.failed_payments }}</span>
                            </td>
                            <td>{{ analytics.installment_payments }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center text-muted">No analytics data available</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block analytics_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Trends Chart
    fetch('{% url "analytics:revenue_trends_api" %}?days=30')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('revenueTrendsChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.dates.map(date => formatDate(date)),
                    datasets: [{
                        label: 'Total Revenue',
                        data: data.total_revenue,
                        borderColor: YITP_COLORS.primary,
                        backgroundColor: YITP_COLORS.primary + '20',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'M-Pesa',
                        data: data.mpesa_revenue,
                        borderColor: YITP_COLORS.success,
                        backgroundColor: 'transparent',
                        tension: 0.4
                    }, {
                        label: 'Bank Transfer',
                        data: data.bank_transfer_revenue,
                        borderColor: YITP_COLORS.info,
                        backgroundColor: 'transparent',
                        tension: 0.4
                    }, {
                        label: 'PayPal',
                        data: data.paypal_revenue,
                        borderColor: YITP_COLORS.warning,
                        backgroundColor: 'transparent',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        });

    // Payment Methods Pie Chart
    const paymentMethodData = {
        labels: ['M-Pesa', 'Bank Transfer', 'PayPal'],
        datasets: [{
            data: [
                {{ payment_methods.method_breakdown.mpesa.revenue }},
                {{ payment_methods.method_breakdown.bank_transfer.revenue }},
                {{ payment_methods.method_breakdown.paypal.revenue }}
            ],
            backgroundColor: [
                YITP_COLORS.success,
                YITP_COLORS.info,
                YITP_COLORS.warning
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    };

    const ctx2 = document.getElementById('paymentMethodsChart').getContext('2d');
    new Chart(ctx2, {
        type: 'doughnut',
        data: paymentMethodData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + formatCurrency(context.raw);
                        }
                    }
                }
            }
        }
    });
});
</script>
{% endblock %}
{% endblock %}
