{% extends 'unified/base.html' %}
{% load static %}

{% block title %}YITP User Flow Documentation{% endblock %}

{% block extra_css %}
<style>
    :root {
        --yitp-orange: #ff5d15;
        --yitp-dark-blue: #1a2e53;
        --yitp-light-gray: #f8f9fa;
        --yitp-border: #dee2e6;
    }

    /* Override any conflicting styles */
    .documentation-page {
        font-family: 'Roboto', sans-serif !important;
    }

    .documentation-page .documentation-header {
        background: linear-gradient(135deg, var(--yitp-orange) 0%, #ff7b3d 100%) !important;
        color: white !important;
        padding: 60px 0 !important;
        margin-bottom: 0 !important;
    }

    .documentation-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 15px;
    }

    .documentation-header p {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .documentation-page .nav-pills .nav-link {
        color: var(--yitp-dark-blue) !important;
        border-radius: 25px !important;
        padding: 12px 24px !important;
        margin: 0 5px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
    }

    .documentation-page .nav-pills .nav-link.active {
        background-color: var(--yitp-orange) !important;
        color: white !important;
    }

    .documentation-page .nav-pills .nav-link:hover {
        background-color: var(--yitp-orange) !important;
        color: white !important;
        transform: translateY(-2px) !important;
    }

    .documentation-page .section-card {
        background: white !important;
        border-radius: 15px !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
        margin-bottom: 30px !important;
        overflow: hidden !important;
        transition: transform 0.3s ease !important;
    }

    .documentation-page .section-card:hover {
        transform: translateY(-5px) !important;
    }

    .documentation-page .section-header {
        background: var(--yitp-dark-blue) !important;
        color: white !important;
        padding: 20px 30px !important;
        margin: 0 !important;
    }

    .documentation-page .section-header h3 {
        margin: 0 !important;
        font-size: 1.5rem !important;
        font-weight: 600 !important;
        color: white !important;
    }

    .section-content {
        padding: 30px;
    }

    .step-card {
        background: var(--yitp-light-gray);
        border-left: 4px solid var(--yitp-orange);
        padding: 20px;
        margin: 15px 0;
        border-radius: 8px;
    }

    .step-number {
        background: var(--yitp-orange);
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 15px;
    }

    .code-snippet {
        background: #2d3748;
        color: #e2e8f0;
        padding: 20px;
        border-radius: 8px;
        margin: 15px 0;
        overflow-x: auto;
    }

    .code-snippet pre {
        margin: 0;
        color: #e2e8f0;
    }

    .mermaid-container {
        background: white;
        border: 2px solid var(--yitp-border);
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        text-align: center;
    }

    .tech-detail {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 15px;
        margin: 15px 0;
        border-radius: 4px;
    }

    .warning-box {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 15px;
        margin: 15px 0;
        border-radius: 4px;
    }

    .success-box {
        background: #d4edda;
        border-left: 4px solid #28a745;
        padding: 15px;
        margin: 15px 0;
        border-radius: 4px;
    }

    .icon-badge {
        background: var(--yitp-orange);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 0.9rem;
        margin-right: 10px;
        display: inline-block;
    }

    .quick-nav {
        position: sticky;
        top: 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 30px;
    }

    .quick-nav h5 {
        color: var(--yitp-dark-blue);
        margin-bottom: 15px;
        font-weight: 600;
    }

    .quick-nav a {
        color: var(--yitp-dark-blue);
        text-decoration: none;
        display: block;
        padding: 8px 0;
        border-bottom: 1px solid var(--yitp-border);
        transition: color 0.3s ease;
    }

    .quick-nav a:hover {
        color: var(--yitp-orange);
    }

    .quick-nav a:last-child {
        border-bottom: none;
    }

    /* Enhanced Hierarchical Navigation Styles */
    .quick-nav .parent-nav {
        font-weight: 600 !important;
        border-bottom: 2px solid var(--yitp-orange) !important;
        margin-bottom: 0 !important;
        cursor: pointer !important;
        position: relative !important;
        transition: all 0.3s ease !important;
        padding: 12px 15px !important;
        border-radius: 8px 8px 0 0 !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        user-select: none !important;
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        /* Button-specific styles */
        border: none !important;
        width: 100% !important;
        text-align: left !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        color: var(--yitp-dark-blue) !important;
        font-size: 1rem !important;
        font-family: inherit !important;
    }

    .quick-nav .parent-nav:hover {
        background: linear-gradient(135deg, var(--yitp-orange) 0%, #e04a0f 100%) !important;
        color: white !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(255, 93, 21, 0.3) !important;
    }

    .quick-nav .parent-nav:focus {
        outline: 3px solid rgba(255, 93, 21, 0.5) !important;
        outline-offset: 2px !important;
    }

    .quick-nav .parent-nav .chevron-icon {
        transition: transform 0.3s ease !important;
        font-size: 0.9rem !important;
        margin-left: auto !important;
    }

    .quick-nav .parent-nav.expanded .chevron-icon {
        transform: rotate(180deg) !important;
    }

    .quick-nav .child-nav-items {
        margin-left: 0 !important;
        border-left: 3px solid var(--yitp-orange) !important;
        margin-bottom: 15px !important;
        overflow: hidden !important;
        background-color: #fff !important;
        border-radius: 0 0 8px 8px !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
        /* Initially hidden */
        display: none !important;
        opacity: 0 !important;
        transform: translateY(-10px) !important;
        transition: all 0.3s ease !important;
    }

    .quick-nav .child-nav-items.expanded {
        display: block !important;
        opacity: 1 !important;
        transform: translateY(0) !important;
    }

    .quick-nav .child-nav {
        padding: 12px 15px 12px 35px !important;
        font-size: 0.9rem !important;
        color: #666 !important;
        background-color: transparent !important;
        border-bottom: 1px solid #e9ecef !important;
        transition: all 0.3s ease !important;
        position: relative !important;
    }

    .quick-nav .child-nav:last-child {
        border-bottom: none !important;
    }

    .quick-nav .child-nav:before {
        content: '' !important;
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        bottom: 0 !important;
        width: 0 !important;
        background-color: var(--yitp-orange) !important;
        transition: width 0.3s ease !important;
    }

    .quick-nav .child-nav:hover {
        background-color: rgba(255, 93, 21, 0.1) !important;
        color: var(--yitp-dark-blue) !important;
        transform: translateX(8px) !important;
        padding-left: 40px !important;
    }

    .quick-nav .child-nav:hover:before {
        width: 4px !important;
    }

    .quick-nav .child-nav:focus {
        outline: 2px solid rgba(255, 93, 21, 0.5) !important;
        outline-offset: 1px !important;
        background-color: rgba(255, 93, 21, 0.1) !important;
    }

    /* Mobile-Friendly Navigation Styles */
    @media (max-width: 768px) {
        .documentation-header h1 {
            font-size: 2rem;
        }

        .documentation-header p {
            font-size: 1rem;
        }

        .section-content {
            padding: 20px;
        }

        .quick-nav {
            position: static;
            margin-bottom: 20px;
        }

        .quick-nav .parent-nav {
            padding: 15px 20px !important;
            font-size: 1rem !important;
            min-height: 48px !important; /* Touch-friendly target size */
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
        }

        .quick-nav .parent-nav .chevron-icon {
            font-size: 1.1rem !important;
            margin-left: 10px !important;
        }

        .quick-nav .child-nav {
            padding: 15px 20px 15px 40px !important;
            font-size: 0.95rem !important;
            min-height: 44px !important; /* Touch-friendly target size */
            display: flex !important;
            align-items: center !important;
        }

        .quick-nav .child-nav:hover {
            transform: translateX(5px) !important;
            padding-left: 45px !important;
        }

        /* Improved touch targets for mobile */
        .quick-nav .parent-nav,
        .quick-nav .child-nav {
            -webkit-tap-highlight-color: rgba(255, 93, 21, 0.2) !important;
            tap-highlight-color: rgba(255, 93, 21, 0.2) !important;
        }
    }

    @media (max-width: 480px) {
        .quick-nav .parent-nav {
            padding: 12px 15px !important;
            font-size: 0.95rem !important;
        }

        .quick-nav .child-nav {
            padding: 12px 15px 12px 35px !important;
            font-size: 0.9rem !important;
        }

        .quick-nav .child-nav:hover {
            padding-left: 40px !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<br><br><br><br><br>

<div class="documentation-page">
<!-- Documentation Header -->
<div class="documentation-header">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 style="color: white;"><i class="fas fa-book-open"></i> YITP User Flow Documentation </h1>
                <p style="color: white;">Comprehensive technical documentation and visual guides for YITP workflows</p>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid py-5">
    <div class="row">
        <!-- Quick Navigation Sidebar -->
        <div class="col-lg-3 col-md-4">
            <div class="quick-nav">
                <h5><i class="fas fa-compass"></i> Quick Navigation</h5>
                <button type="button" class="parent-nav" data-target="#registration-workflow" aria-expanded="false" aria-controls="registration-nav-items" tabindex="0">
                    <span><i class="fas fa-user-plus"></i> User Registration Journey</span>
                    <i class="fas fa-chevron-down chevron-icon" aria-hidden="true"></i>
                </button>
                <div class="child-nav-items" id="registration-nav-items" aria-labelledby="registration-workflow">
                    <a href="#registration-overview" class="child-nav" tabindex="-1"><i class="fas fa-list-ol"></i> Registration Process Steps</a>
                    <a href="#visual-flowchart" class="child-nav" tabindex="-1"><i class="fas fa-project-diagram"></i> User Registration Visual Flow Chart</a>
                    <a href="#email-workflows" class="child-nav" tabindex="-1"><i class="fas fa-envelope"></i> Email Workflow</a>
                    <a href="#welcome-page-experience" class="child-nav" tabindex="-1"><i class="fas fa-home"></i> Welcome Page Experience</a>
                </div>

                <button type="button" class="parent-nav" data-target="#enrollment-workflow" aria-expanded="false" aria-controls="enrollment-nav-items" tabindex="0">
                    <span><i class="fas fa-graduation-cap"></i> User Course Enrollment Journey</span>
                    <i class="fas fa-chevron-down chevron-icon" aria-hidden="true"></i>
                </button>
                <div class="child-nav-items" id="enrollment-nav-items" aria-labelledby="enrollment-workflow">
                    <a href="#login-authentication" class="child-nav" tabindex="-1"><i class="fas fa-sign-in-alt"></i> User Login & Authentication</a>
                    <a href="#enrollment-visual-flow" class="child-nav" tabindex="-1"><i class="fas fa-project-diagram"></i> Complete Enrollment Visual Flow Chart</a>
                    <a href="#profile-navigation" class="child-nav" tabindex="-1"><i class="fas fa-user-cog"></i> Profile Navigation & Management</a>
                    <a href="#course-browsing" class="child-nav" tabindex="-1"><i class="fas fa-search"></i> Course Browsing & Selection</a>
                    <a href="#sponsorship-process" class="child-nav" tabindex="-1"><i class="fas fa-hand-holding-heart"></i> Sponsorship Request Process</a>
                    <a href="#course-enrollment" class="child-nav" tabindex="-1"><i class="fas fa-book-open"></i> Course Enrollment with M-Pesa Integration</a>
                    <a href="#progress-tracking" class="child-nav" tabindex="-1"><i class="fas fa-chart-line"></i> Progress Tracking & Analytics</a>
                    <a href="#email-notifications" class="child-nav" tabindex="-1"><i class="fas fa-envelope"></i> Email Notification System</a>
                </div>

                <button type="button" class="parent-nav" data-target="#learning-journey-workflow" aria-expanded="false" aria-controls="learning-journey-nav-items" tabindex="0">
                    <span><i class="fas fa-graduation-cap"></i> User Learning Journey</span>
                    <i class="fas fa-chevron-down chevron-icon" aria-hidden="true"></i>
                </button>
                <div class="child-nav-items" id="learning-journey-nav-items" aria-labelledby="learning-journey-workflow">
                    <a href="#learning-journey-overview" class="child-nav" tabindex="-1"><i class="fas fa-list-ol"></i> Learning Journey Process Steps</a>
                    <a href="#learning-journey-visual-flow" class="child-nav" tabindex="-1"><i class="fas fa-project-diagram"></i> Complete Learning Visual Flow Chart</a>
                    <a href="#course-access" class="child-nav" tabindex="-1"><i class="fas fa-door-open"></i> Course Access & Getting Started</a>
                    <a href="#lesson-progression" class="child-nav" tabindex="-1"><i class="fas fa-step-forward"></i> Lesson Progression & Content</a>
                    <a href="#quiz-completion" class="child-nav" tabindex="-1"><i class="fas fa-clipboard-check"></i> Quiz Taking & Assessment</a>
                    <a href="#progress-tracking" class="child-nav" tabindex="-1"><i class="fas fa-chart-line"></i> Progress Tracking & Analytics</a>
                    <a href="#course-completion" class="child-nav" tabindex="-1"><i class="fas fa-certificate"></i> Course Completion & Certificates</a>
                </div>




            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9 col-md-8">
            <!-- User Registration Journey Section -->
            <div id="registration-workflow" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-user-plus"></i> User Registration Journey</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Complete overview of the YITP user registration journey, from initial form submission through account activation and welcome experience. This documentation reflects the current implementation validated by our comprehensive test suite with an 80.8% success rate.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Implementation Status:</strong>
                        The registration journey has been thoroughly tested and optimized with robust validation, email failure handling, and seamless user experience. All core functionality is production-ready with comprehensive error handling and fallback mechanisms.
                    </div>
                </div>
            </div>

            <!-- Registration Process Steps Section -->
            <div id="registration-overview" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-list-ol"></i> Registration Process Steps</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Step-by-step breakdown of the YITP user registration journey, from initial form submission through complete account activation and welcome experience.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Current Status:</strong>
                        The registration journey is fully functional and production-ready with comprehensive validation, robust email failure handling, OTP verification system, automatic login, session management, and personalized welcome experience.
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Form Submission & Validation</h5>
                                <p class="mb-0">User fills out registration form with personal details. System validates for duplicate usernames/emails with message "Username already taken. Please choose a different username." and ensures all required fields are completed.</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Account Creation (Inactive)</h5>
                                <p class="mb-0">Django User object created with <code>is_active=False</code> to ensure email verification before platform access</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Profile Creation & Data Storage</h5>
                                <p class="mb-0">User Profile created via Django signals with phone number, personal details, and initial profile completion tracking</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>OTP Generation & Email Delivery</h5>
                                <p class="mb-0">6-digit OTP generated with 200-minute expiry and sent via YITP-branded email using Gmail SMTP. Includes robust email failure handling with fallback mechanisms.</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">5</span>
                            <div>
                                <h5>Email Failure Handling</h5>
                                <p class="mb-0">If email delivery fails, system automatically activates user account and provides warning message, ensuring users can still access the platform while maintaining security.</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">6</span>
                            <div>
                                <h5>OTP Verification & Validation</h5>
                                <p class="mb-0">User enters OTP code with validation for correct format, expiry time, and code accuracy. Includes resend functionality for expired codes.</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">7</span>
                            <div>
                                <h5>Account Activation & Auto-Login</h5>
                                <p class="mb-0">User account activated (<code>is_active=True</code>) upon successful OTP verification with automatic login and session establishment</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">8</span>
                            <div>
                                <h5>Admin Notification & Welcome Email</h5>
                                <p class="mb-0">Admin notification <NAME_EMAIL> about new user registration, followed by branded welcome email to user</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">9</span>
                            <div>
                                <h5>Session Management & Welcome Redirect</h5>
                                <p class="mb-0">Session flag <code>just_completed_otp_verification</code> set for welcome page access control, user redirected to personalized welcome experience</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">10</span>
                            <div>
                                <h5>Welcome Page & Profile Completion</h5>
                                <p class="mb-0">User sees welcome page with next steps, course enrollment options, profile completion tracking, and guided onboarding flow</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Visual Flowchart Section -->
            <div id="visual-flowchart" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-project-diagram"></i> Interactive Visual Flowchart</h3>
                </div>
                <div class="section-content">
                    <p>The following interactive flowchart illustrates the complete user registration journey:</p>

                    <div class="mermaid-container">
                        <div class="mermaid">
                            graph TD
                                A[User Visits Registration Page] --> B[Fills Registration Form]
                                B --> C{Form Validation<br/>Username/Email Check}
                                C -->|Valid| D[Create User Account<br/>is_active=False]
                                C -->|Duplicate Username| E[Show Error: Username already taken]
                                C -->|Other Validation Error| F[Show Validation Errors]
                                E --> B
                                F --> B
                                D --> G[Create User Profile<br/>Store Personal Data]
                                G --> H[Generate 6-digit OTP<br/>200min expiry]
                                H --> I{Email Delivery}
                                I -->|Success| J[Send OTP Email<br/>YITP Branded]
                                I -->|Failure| K[Activate Account<br/>Show Warning Message]
                                J --> L[Redirect to OTP Page]
                                K --> L
                                L --> M[User Enters OTP]
                                M --> N{OTP Valid?}
                                N -->|Valid| O[Activate User Account<br/>is_active=True]
                                N -->|Invalid| P[Show Error Message]
                                N -->|Expired| Q[Show Expired Message]
                                P --> M
                                Q --> R[Resend OTP Option]
                                R --> H
                                O --> S[Auto-Login User<br/>Set Session Flag]
                                S --> T[Send Admin Notification<br/>to <EMAIL>]
                                T --> U[Send Welcome Email]
                                U --> V[Set Session: just_completed_otp_verification]
                                V --> W[Redirect to Welcome Page]
                                W --> X[Welcome Page Experience<br/>Profile Completion & Next Steps]
                                X --> Y[Registration Journey Complete<br/>User Can Access Platform]

                                style A fill:#e3f2fd
                                style D fill:#fff3cd
                                style G fill:#d4edda
                                style H fill:#f3e5f5
                                style J fill:#e8f5e8
                                style K fill:#ffebcd
                                style O fill:#d4edda
                                style S fill:#e8f5e8
                                style T fill:#f0e68c
                                style U fill:#e8f5e8
                                style V fill:#dda0dd
                                style W fill:#fff3cd
                                style X fill:#e3f2fd
                                style Y fill:#c8e6c9
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Technical Note:</strong>
                        This flowchart is interactive and can be zoomed/panned. Each step represents a specific function or process in the YITP codebase.
                    </div>
                </div>
            </div>



            <!-- Email Workflows Section -->
            <div id="email-workflows" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-envelope"></i> Email Workflow</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Overview of the automated email communications and robust failure handling mechanisms during the registration journey.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Email System Status:</strong>
                        The email delivery system includes comprehensive error handling with fallback mechanisms. Uses Gmail SMTP (<EMAIL>) with robust failure detection and user-friendly error recovery.
                    </div>

                    <h4><i class="fas fa-envelope-open-text"></i> OTP Verification Email</h4>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Email Delivery & Failure Handling</h5>
                                <p class="mb-0">Immediately sent after user completes registration form. If delivery fails, system automatically activates account and shows warning message to ensure user access.</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Email Content & Branding</h5>
                                <p class="mb-0">Contains 6-digit verification code with YITP branding, clear instructions, and mobile-responsive HTML/plain text versions</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Security & Validity</h5>
                                <p class="mb-0">OTP code expires after 200 minutes for security with resend functionality for expired codes</p>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt-4"><i class="fas fa-user-shield"></i> Admin Notification Email</h4>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Automatic Admin Alert</h5>
                                <p class="mb-0"><NAME_EMAIL> immediately after successful OTP verification to notify administrators of new user registrations</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>User Information</h5>
                                <p class="mb-0">Contains new user details including name, email, registration timestamp, and profile information for admin tracking</p>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt-4"><i class="fas fa-heart"></i> Welcome Email</h4>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Email Delivery</h5>
                                <p class="mb-0">Sent immediately after successful OTP verification, account activation, and admin notification</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Personalized Welcome Message</h5>
                                <p class="mb-0">Personalized welcome with user's name, YITP program information, and clear next steps for platform engagement</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Program Details & Resources</h5>
                                <p class="mb-0">Information about available courses, sponsorship opportunities, community features, and platform navigation guidance</p>
                            </div>
                        </div>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Email Features:</strong>
                        All emails use YITP branding colors (#ff5d15 orange, #1a2e53 dark blue), are mobile-responsive, include both HTML and plain text versions, and have comprehensive error handling with fallback mechanisms.
                    </div>

                </div>
            </div>

            <!-- Welcome Page Experience Section -->
            <div id="welcome-page-experience" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-home"></i> Welcome Page Experience</h3>
                </div>
                <div class="section-content">
                    <p class="lead">After successful OTP verification and automatic login, users are redirected to a personalized welcome page controlled by session management that guides them through their next steps on the YITP platform.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Session Management:</strong>
                        The welcome page uses session flag <code>just_completed_otp_verification</code> and timestamp tracking to provide a personalized onboarding experience accessible only to newly verified users.
                    </div>

                    <h4><i class="fas fa-star"></i> Welcome Page Features & Session Control</h4>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Session-Based Access Control</h5>
                                <p class="mb-0">Welcome page accessible only with valid session flag <code>just_completed_otp_verification</code> and timestamp, ensuring secure onboarding flow</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Personalized Greeting & Status</h5>
                                <p class="mb-0">Welcome message with user's name, verification confirmation, and account activation status display</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Profile Completion Tracking</h5>
                                <p class="mb-0">Profile completion percentage calculation and guided steps for completing user profile information</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Smart Navigation & Next Steps</h5>
                                <p class="mb-0">Intelligent navigation with direct links to dashboard (/lms/dashboard/), courses (/lms/courses/), and profile (/profile/) based on user status</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">5</span>
                            <div>
                                <h5>Course Enrollment & Platform Access</h5>
                                <p class="mb-0">Direct course browsing and enrollment options with enrollment count tracking and personalized recommendations</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Technical Implementation:</strong>
                        The welcome page uses Django session management, middleware integration, and smart redirect logic to provide a seamless transition from registration to platform engagement while maintaining security and user experience standards.
                    </div>

                </div>
            </div>
            <!-- User Course Enrollment Journey Section -->
            <div id="enrollment-workflow" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-graduation-cap"></i> User Course Enrollment Journey</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Complete overview of the YITP user course enrollment journey, from login through course completion and certification.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Current Status:</strong>
                        The complete user course enrollment journey has been successfully implemented with unified enrollment services, automated payment processing via M-Pesa integration, real-time progress tracking, and automated certificate generation upon course completion.
                    </div>
                </div>
            </div>
            <!-- Login & Authentication Section -->
            <div id="login-authentication" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-sign-in-alt"></i> User Login & Authentication</h3>
                </div>
                <div class="section-content">
                    <p class="lead">How users securely access their YITP account and learning dashboard.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Authentication Status:</strong>
                        The login system is fully secure and working reliably with proper session management and user verification.
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Login Form</h5>
                                <p class="mb-0">User enters their email address and password on the login page</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Credential Verification</h5>
                                <p class="mb-0">System verifies the email and password match an existing account</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Access Granted</h5>
                                <p class="mb-0">User is logged in and redirected to their personalized profile dashboard</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> User Experience:</strong>
                        The login process is streamlined and secure, providing immediate access to all YITP learning features and personal dashboard.
                    </div>
                </div>
            </div>
            <!-- Complete Enrollment Visual Flow Section -->
            <div id="enrollment-visual-flow" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-project-diagram"></i> Interactive Visual Flowchart</h3>
                </div>
                <div class="section-content">
                    <p>The following interactive flowchart illustrates the complete user course enrollment journey:</p>

                    <div class="mermaid-container">
                        <div class="mermaid">
                            graph TD
                                A[User Login] --> B{Authentication Success?}
                                B -->|Valid| C[Access LMS Dashboard]
                                B -->|Invalid| D[Show Login Error]
                                D --> A
                                C --> E[Browse Available Courses]
                                E --> F[Select Course of Interest]
                                F --> G[View Course Details]
                                G --> H{Course Type?}
                                H -->|Free Course| I[Click Enroll Now]
                                H -->|Paid Course| J{Payment Status Check}
                                J -->|Payment Confirmed| I
                                J -->|Payment Required| K[Payment Processing Required]
                                K --> L[Choose Payment Method]
                                L --> M{Payment Method?}
                                M -->|M-Pesa| N[STK Push Initiated]
                                M -->|Bank Transfer| O[Bank Details Provided]
                                N --> P[Complete Phone Payment]
                                O --> Q[Manual Payment Verification]
                                P --> R{Payment Successful?}
                                Q --> R
                                R -->|Yes| S[Update Profile Payment Status]
                                R -->|No| T[Payment Failed - Retry]
                                T --> L
                                S --> U[Payment Confirmation Email]
                                U --> I
                                I --> V[EnrollmentService Validation]
                                V --> W[Create Enrollment Record]
                                W --> X[Send Enrollment Confirmation]
                                X --> Y[Course Access Granted]
                                Y --> Z[Sequential Lesson Access]
                                Z --> AA[Progress Tracking & Analytics]
                                AA --> BB{Course Complete?}
                                BB -->|No| Z
                                BB -->|Yes| CC[Generate Certificate]
                                CC --> DD[Send Completion Email]
                                DD --> EE[Learning Journey Complete]

                                style A fill:#e3f2fd
                                style C fill:#fff3cd
                                style E fill:#d4edda
                                style H fill:#f3e5f5
                                style J fill:#e8f5e8
                                style V fill:#d4edda
                                style W fill:#e8f5e8
                                style Y fill:#fff3cd
                                style AA fill:#e3f2fd
                                style CC fill:#c8e6c9
                                style EE fill:#c8e6c9
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Technical Note:</strong>
                        This flowchart represents the complete user enrollment journey powered by Django's EnrollmentService, featuring automated M-Pesa payment processing, real-time progress tracking, and certificate generation upon course completion.
                    </div>
                </div>
            </div>



            <!-- Profile Navigation Section -->
            <div id="profile-navigation" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-user-cog"></i> Profile Navigation & Management</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Your personalized dashboard where you can manage your learning journey and account settings.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Profile System:</strong>
                        The unified profile provides a comprehensive view of your YITP experience with easy navigation between all features.
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Personal Information</h5>
                                <p class="mb-0">View and update your basic profile details and contact information</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Learning Dashboard</h5>
                                <p class="mb-0">Track your course progress, achievements, and learning milestones</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Course Management</h5>
                                <p class="mb-0">Access your enrolled courses and continue where you left off</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Progress Analytics</h5>
                                <p class="mb-0">View detailed insights about your learning performance and goals</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">5</span>
                            <div>
                                <h5>Account Settings</h5>
                                <p class="mb-0">Manage your payment information, billing history, and account preferences</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Navigation:</strong>
                        All profile sections are easily accessible through intuitive navigation tabs and quick action buttons.
                    </div>
                </div>
            </div>


            <!-- Course Browsing & Selection Section -->
            <div id="course-browsing" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-search"></i> Course Browsing & Selection</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Discover and explore YITP courses that match your learning goals and interests.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Course Discovery:</strong>
                        The course browsing system makes it easy to find the perfect courses for your learning journey with intuitive search and filtering options.
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Browse Course Catalog</h5>
                                <p class="mb-0">Access the complete catalog of available YITP courses from your profile</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Filter and Search</h5>
                                <p class="mb-0">Use filters to find courses by category, difficulty, or price, or search by keywords</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Review Course Details</h5>
                                <p class="mb-0">View comprehensive course information including objectives, instructor, and requirements</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Make Enrollment Decision</h5>
                                <p class="mb-0">Choose to enroll in courses that align with your learning goals</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Course Information:</strong>
                        Each course page provides detailed information to help you make informed enrollment decisions.
                    </div>
                </div>
            </div>
            <!-- Sponsorship Request Process Section -->
            <div id="sponsorship-process" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-hand-holding-heart"></i> Sponsorship Request Process</h3>
                </div>
                <div class="section-content">
                    <p class="lead">How to request financial assistance for YITP programs through our sponsorship system.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Sponsorship System:</strong>
                        The sponsorship request process is designed to be comprehensive yet user-friendly, ensuring all necessary information is collected efficiently.
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Open Request Form</h5>
                                <p class="mb-0">Click the "Request Sponsorship" button on your profile dashboard</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Complete Application</h5>
                                <p class="mb-0">Fill out the sponsorship form with program details and financial information</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Provide Emergency Contact</h5>
                                <p class="mb-0">Add emergency contact information as required for program participation</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Submit Request</h5>
                                <p class="mb-0">Submit your completed sponsorship application for review</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">5</span>
                            <div>
                                <h5>Confirmation & Review</h5>
                                <p class="mb-0">Receive email confirmation and await review from the YITP team</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Application Process:</strong>
                        Your sponsorship request will be carefully reviewed, and you'll receive updates via email throughout the process.
                    </div>
                </div>
            </div>
            <!-- Course Enrollment (Free & Paid) Section -->
            <div id="course-enrollment" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-book-open"></i> Course Enrollment (Free & Paid)</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Simple enrollment process for both free and paid YITP courses.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Enrollment System:</strong>
                        The enrollment process is designed to be quick and straightforward, with automatic verification for payment requirements.
                    </div>

                    <h4><i class="fas fa-gift"></i> Free Course Enrollment</h4>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Click Enroll</h5>
                                <p class="mb-0">Simply click the "Enroll Now" button on any free course page</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Instant Access</h5>
                                <p class="mb-0">You're immediately enrolled and can start learning right away</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Email Confirmation</h5>
                                <p class="mb-0">Receive a confirmation email with course access details</p>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt-4"><i class="fas fa-credit-card"></i> Paid Course Enrollment with M-Pesa Integration</h4>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Payment Method Selection</h5>
                                <p class="mb-0">Choose between M-Pesa mobile payment or bank transfer for course payment</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>M-Pesa STK Push</h5>
                                <p class="mb-0">For M-Pesa payments, receive an instant payment prompt on your phone to complete the transaction</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Payment Confirmation</h5>
                                <p class="mb-0">System automatically verifies M-Pesa payments or processes manual bank transfer verification</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Profile Status Update</h5>
                                <p class="mb-0">Your payment status is updated in your profile, enabling access to paid courses</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">5</span>
                            <div>
                                <h5>Course Enrollment Access</h5>
                                <p class="mb-0">With confirmed payment, you can now enroll in any paid course and start learning immediately</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Payment Integration:</strong>
                        The M-Pesa integration provides seamless mobile payments while bank transfer options ensure accessibility for all users. Payment verification is automated for M-Pesa and manual for bank transfers.
                    </div>
                </div>
            </div>

            <!-- Progress Tracking & Analytics Section -->
            <div id="progress-tracking" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-chart-line"></i> Progress Tracking & Analytics</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Track your learning progress with sequential lesson access, real-time analytics, and automated certificate generation upon course completion.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Advanced Progress Tracking:</strong>
                        The system features sequential lesson progression, cross-module navigation, completion tracking, and automatic certificate generation when courses are completed.
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Sequential Lesson Access</h5>
                                <p class="mb-0">Complete lessons in order - each lesson unlocks the next one, ensuring structured learning progression</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Real-Time Progress Tracking</h5>
                                <p class="mb-0">View your completion percentage and track progress across all enrolled courses</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Cross-Module Navigation</h5>
                                <p class="mb-0">Navigate seamlessly between modules while maintaining lesson completion requirements</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Learning Analytics Dashboard</h5>
                                <p class="mb-0">View comprehensive learning metrics including time spent, completion rates, and performance data</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">5</span>
                            <div>
                                <h5>Automatic Course Completion</h5>
                                <p class="mb-0">When all lessons are completed, the system automatically marks the course as complete</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">6</span>
                            <div>
                                <h5>Certificate Generation</h5>
                                <p class="mb-0">Upon course completion, receive an automatically generated certificate via email</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Smart Progress System:</strong>
                        The system automatically tracks lesson completion, calculates progress percentages, and triggers certificate generation when courses are completed, with email notifications sent for all major milestones.
                    </div>
                </div>
            </div>

            <!-- Email Notification System Section -->
            <div id="email-notifications" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-envelope"></i> Email Notification System</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Comprehensive email notification system that keeps users and administrators informed throughout the entire enrollment and learning journey.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Automated Communications:</strong>
                        The system automatically sends targeted email notifications for enrollment confirmations, payment updates, course completion, and certificate issuance.
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Enrollment Confirmation Emails</h5>
                                <p class="mb-0">Students receive detailed enrollment confirmation emails with course information and access instructions</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Admin Enrollment Notifications</h5>
                                <p class="mb-0">Administrators receive comprehensive notifications about new enrollments including student details and course statistics</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Payment Confirmation Emails</h5>
                                <p class="mb-0">Automated payment confirmation emails are sent when M-Pesa or bank transfer payments are successfully processed</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Course Completion Notifications</h5>
                                <p class="mb-0">Students receive congratulatory emails when they complete courses, including achievement summaries</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">5</span>
                            <div>
                                <h5>Certificate Issuance Emails</h5>
                                <p class="mb-0">Automated certificate delivery emails with downloadable certificates are sent upon course completion</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Email Integration:</strong>
                        All emails are sent using Django's email framework with HTML and plain text versions, ensuring compatibility across all email clients and providing professional communication throughout the learning journey.
                    </div>
                </div>
            </div>

            <!-- User Learning Journey Section -->
            <div id="learning-journey-workflow" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-graduation-cap"></i> User Learning Journey</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Complete overview of how students access course content and progress through lessons in a structured, sequential learning experience leading to course completion.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Current Status:</strong>
                        The complete learning journey system has been successfully implemented and tested, providing students with a seamless experience from first lesson access through course completion and certificate issuance.
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-cogs"></i> Enhanced Features:</strong>
                        The system includes intelligent lesson progression, interactive assessments, real-time progress tracking, and automatic certificate generation, creating a comprehensive learning experience that guides students to success.
                    </div>
                </div>
            </div>

            <!-- Learning Journey Process Steps Section -->
            <div id="learning-journey-overview" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-list-ol"></i> Learning Journey Process Steps</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Step-by-step breakdown of how students experience their complete learning journey, from accessing their first lesson to receiving their course completion certificate.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Learning Experience:</strong>
                        The learning journey provides a structured, engaging path that builds knowledge progressively while maintaining student motivation through clear milestones and achievements.
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Course Access & Getting Started</h5>
                                <p class="mb-0">Students log in and access their enrolled courses through a personalized dashboard that shows their learning progress and available content</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>First Lesson Discovery</h5>
                                <p class="mb-0">Students easily find and access their first lesson, which is automatically available without any prerequisites, allowing them to begin learning immediately</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Interactive Content Engagement</h5>
                                <p class="mb-0">Students engage with rich multimedia content including text, images, videos, and interactive elements, all presented with YITP's professional branding</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Knowledge Assessment</h5>
                                <p class="mb-0">Students complete quizzes and assessments that test their understanding, with immediate feedback and multiple attempts to ensure mastery</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">5</span>
                            <div>
                                <h5>Sequential Progression</h5>
                                <p class="mb-0">Students unlock new lessons automatically as they complete previous ones, ensuring they build knowledge in the proper sequence</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">6</span>
                            <div>
                                <h5>Progress Tracking</h5>
                                <p class="mb-0">Students can monitor their learning progress through visual indicators, completion percentages, and achievement badges that motivate continued learning</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">7</span>
                            <div>
                                <h5>Course Completion & Certification</h5>
                                <p class="mb-0">Upon completing all course requirements, students automatically receive their completion certificate and can access advanced courses or additional learning opportunities</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Student-Centered Design:</strong>
                        Every aspect of the learning journey is designed with the student experience in mind, providing clear guidance, immediate feedback, and meaningful recognition of achievements.
                    </div>
                </div>
            </div>

            <!-- Complete Learning Visual Flow Chart Section -->
            <div id="learning-journey-visual-flow" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-project-diagram"></i> Complete Learning Visual Flow Chart</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Interactive visual representation of the complete student learning journey, showing the flow from course access through completion and certification.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Interactive Flowchart:</strong>
                        This Mermaid.js flowchart provides a comprehensive visual guide to understanding how students progress through their complete learning experience in the YITP learning management system.
                    </div>

                    <div class="flowchart-container">
                        <div class="mermaid">
                        flowchart TD
                            A[Student Logs In] --> B[Access Course Dashboard]
                            B --> C[View Enrolled Courses]
                            C --> D[Select Course to Study]
                            D --> E[View Course Modules]

                            E --> F[Access First Lesson]
                            F --> G[Read Lesson Content]
                            G --> H[Engage with Multimedia]
                            H --> I{Lesson Complete?}

                            I -->|Yes| J[Mark Lesson as Complete]
                            I -->|No| G

                            J --> K{Quiz Available?}
                            K -->|Yes| L[Take Quiz]
                            K -->|No| M[Check Next Lesson]

                            L --> N{Quiz Passed?}
                            N -->|Yes| O[Receive Quiz Feedback]
                            N -->|No| P[Review Incorrect Answers]

                            P --> Q{Retake Available?}
                            Q -->|Yes| L
                            Q -->|No| R[Study Additional Resources]
                            R --> L

                            O --> M
                            M --> S{More Lessons Available?}
                            S -->|Yes| T[Unlock Next Lesson]
                            S -->|No| U{Module Complete?}

                            T --> F
                            U -->|Yes| V[Progress to Next Module]
                            U -->|No| W[Continue Current Module]

                            V --> E
                            W --> F

                            U -->|All Modules Complete| X[Course Completion Check]
                            X --> Y[Generate Certificate]
                            Y --> Z[Send Completion Email]
                            Z --> AA[Update Student Records]
                            AA --> BB[Display Achievement Badge]
                            BB --> CC[Unlock Advanced Courses]

                            %% Styling with YITP colors
                            classDef startEnd fill:#ff5d15,stroke:#1a2e53,stroke-width:3px,color:#fff
                            classDef process fill:#1a2e53,stroke:#ff5d15,stroke-width:2px,color:#fff
                            classDef decision fill:#f8f9fa,stroke:#ff5d15,stroke-width:2px,color:#1a2e53
                            classDef success fill:#28a745,stroke:#1a2e53,stroke-width:2px,color:#fff
                            classDef warning fill:#ffc107,stroke:#1a2e53,stroke-width:2px,color:#1a2e53

                            class A,CC startEnd
                            class B,C,D,E,F,G,H,J,L,O,T,V,X,Y,Z,AA,BB process
                            class I,K,N,Q,S,U decision
                            class O,Y,BB,CC success
                            class P,R warning
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Flow Explanation:</strong>
                        This flowchart illustrates the complete learning experience, showing how students progress through content, assessments, and achievements in a structured, supportive environment that promotes learning success.
                    </div>
                </div>
            </div>

            <!-- Course Access & Getting Started Section -->
            <div id="course-access" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-door-open"></i> Course Access & Getting Started</h3>
                </div>
                <div class="section-content">
                    <p class="lead">How students begin their learning journey by accessing courses and navigating to their first lesson.</p>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Dashboard Overview</h5>
                                <p class="mb-0">Students see a personalized dashboard showing their enrolled courses, progress statistics, and recent activity</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Course Selection</h5>
                                <p class="mb-0">Students choose which course to study from their enrollment list, with clear progress indicators for each course</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Module Navigation</h5>
                                <p class="mb-0">Students view the course structure organized into modules, with clear indication of which lessons are available to access</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> User-Friendly Design:</strong>
                        The interface is designed to be intuitive and welcoming, helping students feel confident as they begin their learning journey.
                    </div>
                </div>
            </div>

            <!-- Lesson Progression & Content Section -->
            <div id="lesson-progression" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-step-forward"></i> Lesson Progression & Content</h3>
                </div>
                <div class="section-content">
                    <p class="lead">How students engage with lesson content and progress through the sequential learning structure.</p>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Lesson Content Display</h5>
                                <p class="mb-0">Students view rich, multimedia lesson content with professional YITP branding, including text, images, videos, and interactive elements</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Progress Tracking</h5>
                                <p class="mb-0">Students can track their time spent on lessons and see their overall progress through visual indicators and completion percentages</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Lesson Completion</h5>
                                <p class="mb-0">Students mark lessons as complete when finished, which automatically unlocks the next lesson in the sequence</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Sequential Unlocking</h5>
                                <p class="mb-0">New lessons become available only after completing previous ones, ensuring students build knowledge in the proper order</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Structured Learning:</strong>
                        The sequential progression ensures students master each concept before moving forward, building a solid foundation of knowledge.
                    </div>
                </div>
            </div>

            <!-- Quiz Taking & Assessment Section -->
            <div id="quiz-completion" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-clipboard-check"></i> Quiz Taking & Assessment</h3>
                </div>
                <div class="section-content">
                    <p class="lead">How students complete assessments and receive feedback on their learning progress.</p>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Quiz Discovery</h5>
                                <p class="mb-0">Students find quizzes integrated into their lesson sequence, with clear information about requirements and expectations</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Assessment Experience</h5>
                                <p class="mb-0">Students take quizzes in a focused, distraction-free environment with multiple question types and clear instructions</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Immediate Feedback</h5>
                                <p class="mb-0">Students receive instant results and detailed feedback, helping them understand correct answers and learn from mistakes</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Retake Opportunities</h5>
                                <p class="mb-0">Students can retake quizzes if needed, with multiple attempts available to ensure mastery of the material</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Assessment Excellence:</strong>
                        The assessment system focuses on learning rather than just testing, providing opportunities for improvement and mastery.
                    </div>
                </div>
            </div>

            <!-- Progress Tracking & Analytics Section -->
            <div id="progress-tracking" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-chart-line"></i> Progress Tracking & Analytics</h3>
                </div>
                <div class="section-content">
                    <p class="lead">How students monitor their learning progress and access detailed analytics about their performance.</p>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Progress Dashboard</h5>
                                <p class="mb-0">Students access a comprehensive dashboard showing their progress across all courses, with visual indicators and completion percentages</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Learning Analytics</h5>
                                <p class="mb-0">Students view detailed analytics including time spent learning, quiz performance, and learning streaks that motivate continued engagement</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Achievement Recognition</h5>
                                <p class="mb-0">Students earn badges and achievements for reaching milestones, maintaining learning streaks, and completing challenging content</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Performance Insights</h5>
                                <p class="mb-0">Students receive personalized insights about their learning patterns and suggestions for improvement based on their performance data</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Data-Driven Learning:</strong>
                        The analytics system helps students understand their learning patterns and make informed decisions about their study approach.
                    </div>
                </div>
            </div>

            <!-- Course Completion & Certificates Section -->
            <div id="course-completion" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-certificate"></i> Course Completion & Certificates</h3>
                </div>
                <div class="section-content">
                    <p class="lead">How students complete their courses and receive recognition for their achievements through certificates and credentials.</p>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">1</span>
                            <div>
                                <h5>Completion Requirements</h5>
                                <p class="mb-0">Students can clearly see what requirements remain for course completion, including lessons, quizzes, and any final assessments</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">2</span>
                            <div>
                                <h5>Automatic Completion Detection</h5>
                                <p class="mb-0">The system automatically recognizes when students have met all course requirements and triggers the completion process</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Certificate Generation</h5>
                                <p class="mb-0">Students automatically receive a professional completion certificate with their name, course details, and completion date</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Completion Notification</h5>
                                <p class="mb-0">Students receive email notifications celebrating their achievement and providing information about next steps or advanced courses</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">5</span>
                            <div>
                                <h5>Continued Learning Opportunities</h5>
                                <p class="mb-0">Students are presented with recommendations for additional courses and learning paths to continue their educational journey</p>
                            </div>
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Achievement Recognition:</strong>
                        The completion process celebrates student success and provides meaningful credentials that recognize their learning accomplishments.
                    </div>
                </div>
            </div>

















        </div>
    </div>
</div>
</div> <!-- Close documentation-page wrapper -->
{% endblock %}

{% block extra_js %}
<!-- Mermaid.js for Interactive Flowcharts -->
<script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
<script>
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Initializing Mermaid.js...');

        // Initialize Mermaid with YITP theme
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#ff5d15',
                primaryTextColor: '#1a2e53',
                primaryBorderColor: '#ff5d15',
                lineColor: '#1a2e53',
                secondaryColor: '#f8f9fa',
                tertiaryColor: '#e3f2fd'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // Force render Mermaid diagrams
        setTimeout(function() {
            mermaid.init();
            console.log('Mermaid diagrams initialized');
        }, 1000);

        // Enhanced Hierarchical Navigation Functionality
        function initializeDropdownNavigation() {
            const parentNavs = document.querySelectorAll('.parent-nav');

            if (parentNavs.length === 0) {
                return;
            }

            parentNavs.forEach((parentNav) => {
                const targetId = parentNav.getAttribute('aria-controls');
                const childNavItems = document.getElementById(targetId);
                const chevron = parentNav.querySelector('.chevron-icon');
                const childLinks = childNavItems ? childNavItems.querySelectorAll('.child-nav') : [];

                // Validate required elements
                if (!childNavItems || !chevron) {
                    return;
                }

                // Click/Touch event handler
                parentNav.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleDropdown(parentNav, childNavItems, chevron, childLinks);
                });

                // Keyboard event handler
                parentNav.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleDropdown(parentNav, childNavItems, chevron, childLinks);
                    }
                });
            });
        }

        function toggleDropdown(parentNav, childNavItems, chevron, childLinks) {
            const isExpanded = parentNav.getAttribute('aria-expanded') === 'true';

            if (isExpanded) {
                closeDropdown(parentNav, childNavItems, chevron, childLinks);
            } else {
                openDropdown(parentNav, childNavItems, chevron, childLinks);
            }
        }

        function openDropdown(parentNav, childNavItems, chevron, childLinks) {
            // Close all other dropdowns first
            closeAllDropdowns();

            // Open this dropdown
            parentNav.setAttribute('aria-expanded', 'true');
            childNavItems.style.maxHeight = childNavItems.scrollHeight + 'px';
            childNavItems.style.opacity = '1';
            childNavItems.style.visibility = 'visible';
            chevron.style.transform = 'rotate(180deg)';

            // Enable child links
            childLinks.forEach(link => {
                link.setAttribute('tabindex', '0');
            });
        }

        function closeDropdown(parentNav, childNavItems, chevron, childLinks) {
            parentNav.setAttribute('aria-expanded', 'false');
            childNavItems.style.maxHeight = '0';
            childNavItems.style.opacity = '0';
            childNavItems.style.visibility = 'hidden';
            if (chevron) {
                chevron.style.transform = 'rotate(0deg)';
            }

            // Disable child links
            if (childLinks) {
                childLinks.forEach(link => {
                    link.setAttribute('tabindex', '-1');
                });
            }
        }

        function closeAllDropdowns() {
            const allParentNavs = document.querySelectorAll('.parent-nav');
            allParentNavs.forEach(parentNav => {
                const targetId = parentNav.getAttribute('aria-controls');
                const childNavItems = document.getElementById(targetId);
                const childLinks = childNavItems ? childNavItems.querySelectorAll('.child-nav') : [];

                if (childNavItems) {
                    closeDropdown(parentNav, childNavItems, null, childLinks);
                }
            });
        }

        // Initialize dropdown navigation
        initializeDropdownNavigation();

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.quick-nav')) {
                closeAllDropdowns();
            }
        });

        // Enhanced smooth scrolling for navigation links
        document.querySelectorAll('.quick-nav .child-nav[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    // Close all dropdowns after navigation
                    closeAllDropdowns();

                    // Smooth scroll to target
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Focus management for accessibility
                    setTimeout(() => {
                        target.focus();
                    }, 500);
                }
            });
        });

        // Highlight active section in navigation
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section-card');
            const navLinks = document.querySelectorAll('.quick-nav a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    });
</script>
{% endblock %}
                        <div class="d-flex align-items-center">
                            <span class="step-number">3</span>
                            <div>
                                <h5>Automatic Course Status Update</h5>
                                <p class="mb-0">Enrollment status automatically updates to 'completed' with completion timestamp, triggering certificate eligibility assessment and achievement recognition</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">4</span>
                            <div>
                                <h5>Certificate Eligibility Assessment</h5>
                                <p class="mb-0">System uses <code>is_eligible_for_certificate()</code> method to verify completion status and minimum performance requirements before certificate generation</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">5</span>
                            <div>
                                <h5>Automatic Certificate Generation</h5>
                                <p class="mb-0">Certificate is automatically generated using <code>generate_certificate()</code> method with unique verification code, completion date, and final performance score</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">6</span>
                            <div>
                                <h5>Completion Notification Delivery</h5>
                                <p class="mb-0">Student receives congratulatory email with certificate attachment, achievement summary, and instructions for credential verification and sharing</p>
                            </div>
                        </div>
                    </div>

                    <div class="step-card">
                        <div class="d-flex align-items-center">
                            <span class="step-number">7</span>
                            <div>
                                <h5>Post-Completion Benefits Activation</h5>
                                <p class="mb-0">Access to alumni resources, advanced courses, and professional networking opportunities becomes available upon successful course completion</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Certificate Issuance Visual Flow Chart Section -->
            <div id="certificate-completion-visual-flow" class="section-card">
                <div class="section-header">
                    <h3><i class="fas fa-project-diagram"></i> Interactive Certificate Issuance Visual Flow Chart</h3>
                </div>
                <div class="section-content">
                    <p class="lead">Interactive visual representation of the complete certificate issuance and course completion workflow, showing the journey from final lesson to verified credential.</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Interactive Flowchart:</strong>
                        This Mermaid.js flowchart provides a comprehensive visual guide to understanding how students complete courses and receive certificates in the YITP learning management system.
                    </div>

                    <div class="flowchart-container">
                        <div class="mermaid">
                        flowchart TD
                            A[Student Completes Final Lesson] --> B[Mark Last Lesson Complete]
                            B --> C{Check All Lessons Completed}
                            C -->|Yes| D[Update Enrollment Status to 'Completed']
                            C -->|No| E[Continue Learning Journey]
                            E --> F[Return to Course Dashboard]

                            D --> G[Set Completion Timestamp]
                            G --> H{Check Certificate Eligibility}
                            H -->|Eligible| I[Generate Certificate]
                            H -->|Not Eligible| J[Update Progress Only]

                            I --> K[Create Unique Verification Code]
                            K --> L[Calculate Final Performance Score]
                            L --> M[Generate PDF Certificate]
                            M --> N[Store Certificate in Database]

                            N --> O[Send Completion Email]
                            O --> P[Include Certificate Attachment]
                            P --> Q[Provide Verification Instructions]

                            Q --> R[Update Student Analytics]
                            R --> S[Record Achievement Data]
                            S --> T[Calculate Learning Streak]

                            T --> U[Unlock Post-Completion Features]
                            U --> V[Alumni Resource Access]
                            V --> W[Advanced Course Recommendations]
                            W --> X[Professional Networking Opportunities]

                            J --> Y[Send Progress Update Email]
                            Y --> Z[Provide Improvement Guidance]
                            Z --> AA[Return to Course Dashboard]

                            X --> BB[Certificate Verification Portal]
                            BB --> CC[Employer/Institution Verification]
                            CC --> DD[Professional Credential Recognition]

                            %% Styling with YITP colors
                            classDef startEnd fill:#ff5d15,stroke:#1a2e53,stroke-width:3px,color:#fff
                            classDef process fill:#1a2e53,stroke:#ff5d15,stroke-width:2px,color:#fff
                            classDef decision fill:#f8f9fa,stroke:#ff5d15,stroke-width:2px,color:#1a2e53
                            classDef success fill:#28a745,stroke:#1a2e53,stroke-width:2px,color:#fff
                            classDef warning fill:#ffc107,stroke:#1a2e53,stroke-width:2px,color:#1a2e53
                            classDef certificate fill:#6f42c1,stroke:#1a2e53,stroke-width:2px,color:#fff

                            class A,DD startEnd
                            class B,D,G,I,K,L,M,N,O,P,Q,R,S,T,U,V,W,Y,Z,BB,CC process
                            class C,H decision
                            class X,AA,F success
                            class J warning
                            class I,K,L,M,N certificate
                        </div>
                    </div>

                    <div class="tech-detail">
                        <strong><i class="fas fa-info-circle"></i> Flow Explanation:</strong>
                        This flowchart illustrates the complete certificate issuance process, from final lesson completion through credential verification, ensuring students receive proper recognition and verifiable credentials for their learning achievements.
                    </div>
                </div>
            </div>









        </div>
    </div>
</div>
</div> <!-- Close documentation-page wrapper -->
{% endblock %}

{% block extra_js %}
<!-- Mermaid.js for Interactive Flowcharts -->
<script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
<script>
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Initializing Mermaid.js...');

        // Initialize Mermaid with YITP theme
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#ff5d15',
                primaryTextColor: '#1a2e53',
                primaryBorderColor: '#ff5d15',
                lineColor: '#1a2e53',
                secondaryColor: '#f8f9fa',
                tertiaryColor: '#e3f2fd'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // Force render Mermaid diagrams
        setTimeout(function() {
            mermaid.init();
            console.log('Mermaid diagrams initialized');
        }, 1000);

        // Enhanced Hierarchical Navigation Functionality
        function initializeDropdownNavigation() {
            const parentNavs = document.querySelectorAll('.parent-nav');

            if (parentNavs.length === 0) {
                return;
            }

            parentNavs.forEach((parentNav) => {
                const targetId = parentNav.getAttribute('aria-controls');
                const childNavItems = document.getElementById(targetId);
                const chevron = parentNav.querySelector('.chevron-icon');
                const childLinks = childNavItems ? childNavItems.querySelectorAll('.child-nav') : [];

                // Validate required elements
                if (!childNavItems || !chevron) {
                    return;
                }

                // Click/Touch event handler
                parentNav.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleDropdown(parentNav, childNavItems, chevron, childLinks);
                });

                // Keyboard event handler
                parentNav.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleDropdown(parentNav, childNavItems, chevron, childLinks);
                    }
                });
            });
        }

        function toggleDropdown(parentNav, childNavItems, chevron, childLinks) {
            const isExpanded = parentNav.getAttribute('aria-expanded') === 'true';

            if (isExpanded) {
                closeDropdown(parentNav, childNavItems, chevron, childLinks);
            } else {
                openDropdown(parentNav, childNavItems, chevron, childLinks);
            }
        }

        function openDropdown(parentNav, childNavItems, chevron, childLinks) {
            // Close all other dropdowns first
            closeAllDropdowns();

            // Open this dropdown
            parentNav.setAttribute('aria-expanded', 'true');
            childNavItems.style.maxHeight = childNavItems.scrollHeight + 'px';
            childNavItems.style.opacity = '1';
            childNavItems.style.visibility = 'visible';
            chevron.style.transform = 'rotate(180deg)';

            // Enable child links
            childLinks.forEach(link => {
                link.setAttribute('tabindex', '0');
            });
        }

        function closeDropdown(parentNav, childNavItems, chevron, childLinks) {
            parentNav.setAttribute('aria-expanded', 'false');
            childNavItems.style.maxHeight = '0';
            childNavItems.style.opacity = '0';
            childNavItems.style.visibility = 'hidden';
            if (chevron) {
                chevron.style.transform = 'rotate(0deg)';
            }

            // Disable child links
            if (childLinks) {
                childLinks.forEach(link => {
                    link.setAttribute('tabindex', '-1');
                });
            }
        }

        function closeAllDropdowns() {
            const allParentNavs = document.querySelectorAll('.parent-nav');
            allParentNavs.forEach(parentNav => {
                const targetId = parentNav.getAttribute('aria-controls');
                const childNavItems = document.getElementById(targetId);
                const childLinks = childNavItems ? childNavItems.querySelectorAll('.child-nav') : [];

                if (childNavItems) {
                    closeDropdown(parentNav, childNavItems, null, childLinks);
                }
            });
        }

        // Initialize dropdown navigation
        initializeDropdownNavigation();

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.quick-nav')) {
                closeAllDropdowns();
            }
        });

        // Enhanced smooth scrolling for navigation links
        document.querySelectorAll('.quick-nav .child-nav[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    // Close all dropdowns after navigation
                    closeAllDropdowns();

                    // Smooth scroll to target
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Focus management for accessibility
                    setTimeout(() => {
                        target.focus();
                    }, 500);
                }
            });
        });

        // Highlight active section in navigation
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section-card');
            const navLinks = document.querySelectorAll('.quick-nav a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    });
</script>


{% block extra_js %}
<!-- Mermaid.js for Interactive Flowcharts -->
<script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
<script>
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Initializing Mermaid.js...');

        // Initialize Mermaid with YITP theme
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#ff5d15',
                primaryTextColor: '#1a2e53',
                primaryBorderColor: '#ff5d15',
                lineColor: '#1a2e53',
                secondaryColor: '#f8f9fa',
                tertiaryColor: '#e3f2fd'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // Force render Mermaid diagrams
        setTimeout(function() {
            mermaid.init();
            console.log('Mermaid diagrams initialized');
        }, 1000);

        // Enhanced Hierarchical Navigation Functionality
        function initializeDropdownNavigation() {
            const parentNavs = document.querySelectorAll('.parent-nav');

            if (parentNavs.length === 0) {
                return;
            }

            parentNavs.forEach((parentNav) => {
                const targetId = parentNav.getAttribute('aria-controls');
                const childNavItems = document.getElementById(targetId);
                const chevron = parentNav.querySelector('.chevron-icon');
                const childLinks = childNavItems ? childNavItems.querySelectorAll('.child-nav') : [];

                // Validate required elements
                if (!childNavItems || !chevron) {
                    return;
                }

                // Click/Touch event handler
                parentNav.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleDropdown(parentNav, childNavItems, chevron, childLinks);
                });

                // Keyboard event handler
                parentNav.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        toggleDropdown(parentNav, childNavItems, chevron, childLinks);
                    } else if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (!parentNav.classList.contains('expanded')) {
                            toggleDropdown(parentNav, childNavItems, chevron, childLinks);
                        }
                        // Focus first child link
                        const firstChild = childNavItems.querySelector('.child-nav');
                        if (firstChild) {
                            firstChild.focus();
                        }
                    }
                });

                // Handle keyboard navigation within child items
                childLinks.forEach((link, index) => {
                    link.addEventListener('keydown', function(e) {
                        if (e.key === 'ArrowDown') {
                            e.preventDefault();
                            const nextLink = childLinks[index + 1];
                            if (nextLink) {
                                nextLink.focus();
                            }
                        } else if (e.key === 'ArrowUp') {
                            e.preventDefault();
                            if (index === 0) {
                                parentNav.focus();
                            } else {
                                const prevLink = childLinks[index - 1];
                                if (prevLink) {
                                    prevLink.focus();
                                }
                            }
                        } else if (e.key === 'Escape') {
                            e.preventDefault();
                            closeDropdown(parentNav, childNavItems, chevron, childLinks);
                            parentNav.focus();
                        }
                    });
                });
            });
        }

        function toggleDropdown(parentNav, childNavItems, chevron, childLinks) {
            const isExpanded = parentNav.classList.contains('expanded');

            // Close all other dropdowns first (accordion behavior)
            closeAllDropdowns();

            // If this dropdown wasn't expanded, open it
            if (!isExpanded) {
                openDropdown(parentNav, childNavItems, chevron, childLinks);
            }
        }

        function openDropdown(parentNav, childNavItems, chevron, childLinks) {
            parentNav.classList.add('expanded');
            parentNav.setAttribute('aria-expanded', 'true');

            // Show the dropdown with animation
            childNavItems.style.display = 'block';
            // Force reflow to ensure display change takes effect
            childNavItems.offsetHeight;
            // Add expanded class for animation
            childNavItems.classList.add('expanded');

            // Enable tab navigation for child links
            childLinks.forEach(link => {
                link.setAttribute('tabindex', '0');
            });
        }

        function closeDropdown(parentNav, childNavItems, chevron, childLinks) {
            parentNav.classList.remove('expanded');
            parentNav.setAttribute('aria-expanded', 'false');

            // Remove expanded class first for animation
            childNavItems.classList.remove('expanded');

            // Hide after animation completes
            setTimeout(() => {
                if (!childNavItems.classList.contains('expanded')) {
                    childNavItems.style.display = 'none';
                }
            }, 300);

            // Disable tab navigation for child links
            childLinks.forEach(link => {
                link.setAttribute('tabindex', '-1');
            });
        }

        function closeAllDropdowns() {
            const allParentNavs = document.querySelectorAll('.parent-nav');
            allParentNavs.forEach(parentNav => {
                const targetId = parentNav.getAttribute('aria-controls');
                const childNavItems = document.getElementById(targetId);
                const childLinks = childNavItems ? childNavItems.querySelectorAll('.child-nav') : [];

                if (childNavItems) {
                    closeDropdown(parentNav, childNavItems, null, childLinks);
                }
            });
        }

        // Initialize dropdown navigation
        initializeDropdownNavigation();

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.quick-nav')) {
                closeAllDropdowns();
            }
        });

        // Enhanced smooth scrolling for navigation links
        document.querySelectorAll('.quick-nav .child-nav[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    // Close all dropdowns after navigation
                    closeAllDropdowns();

                    // Smooth scroll to target
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Focus management for accessibility
                    setTimeout(() => {
                        target.focus();
                    }, 500);
                }
            });
        });

        // Highlight active section in navigation
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section-card');
            const navLinks = document.querySelectorAll('.quick-nav a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // Add copy functionality to code snippets
        document.querySelectorAll('.code-snippet').forEach(snippet => {
            const copyBtn = document.createElement('button');
            copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy';
            copyBtn.className = 'btn btn-sm btn-outline-light position-absolute';
            copyBtn.style.top = '10px';
            copyBtn.style.right = '10px';

            snippet.style.position = 'relative';
            snippet.appendChild(copyBtn);

            copyBtn.addEventListener('click', function() {
                const code = snippet.querySelector('pre code').textContent;
                navigator.clipboard.writeText(code).then(() => {
                    copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    setTimeout(() => {
                        copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy';
                    }, 2000);
                });
            });
        });

        // Add expand/collapse functionality to sections
        document.querySelectorAll('.section-header').forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                const content = this.nextElementSibling;
                const icon = document.createElement('i');

                if (content.style.display === 'none') {
                    content.style.display = 'block';
                    icon.className = 'fas fa-chevron-up float-right';
                } else {
                    content.style.display = 'none';
                    icon.className = 'fas fa-chevron-down float-right';
                }

                // Remove existing icon and add new one
                const existingIcon = this.querySelector('.float-right');
                if (existingIcon) existingIcon.remove();
                this.appendChild(icon);
            });
        });

        console.log('Documentation page JavaScript initialized successfully');
    }); // Close DOMContentLoaded event listener
</script>

<style>
    .quick-nav a.active {
        color: var(--yitp-orange) !important;
        font-weight: 600;
        border-left: 3px solid var(--yitp-orange);
        padding-left: 15px;
    }

    .code-snippet:hover .btn {
        opacity: 1;
    }

    .code-snippet .btn {
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .section-header:hover {
        background-color: #152238 !important;
    }
</style>
{% endblock %}
