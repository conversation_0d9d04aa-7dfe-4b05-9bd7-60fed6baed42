{% extends "emails/base_email.html" %}

{% block title %}Welcome to YITP Instructor Team - Account Created{% endblock %}

{% block content %}
<div class="greeting">
    🎓 Welcome to the YITP Instructor Team, {{ user.first_name|default:user.username }}!
</div>

<div class="content-text">
    Congratulations! Your instructor account has been successfully created for the <strong>Youth Impact Training Programme</strong>. We're excited to have you join our team of dedicated educators and mentors.
</div>

<div class="success-box">
    <strong>🚀 Your Instructor Journey Begins Now!</strong><br>
    You've been granted access to our Learning Management System with specialized instructor privileges. Let's get you started!
</div>

<!-- Login Credentials Section -->
<div class="info-box">
    <strong>🔐 Your Login Credentials:</strong><br>
    <strong>Username:</strong> {{ user.username }}<br>
    <strong>Email:</strong> {{ user.email }}<br>
    {% if temporary_password %}<strong>Temporary Password:</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace;">{{ temporary_password }}</code><br>{% endif %}
    <strong>Login URL:</strong> <a href="{{ login_url }}" style="color: #ff5d15; text-decoration: none;">{{ login_url }}</a>
</div>

<!-- Account Details Section -->
<div class="info-box" style="background-color: #fff3e0; border-left: 4px solid #ff5d15;">
    <strong>👤 Your Instructor Role & Permissions:</strong><br>
    <strong>Role:</strong> <span style="background: {{ role_color }}; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; font-weight: bold;">{{ instructor_role_display }}</span><br>
    <strong>Verification Status:</strong> 
    {% if verification_status == 'verified' %}
        <span style="color: #28a745; font-weight: bold;">✅ Verified</span>
    {% elif verification_status == 'pending' %}
        <span style="color: #ffc107; font-weight: bold;">⏳ Pending Verification</span>
    {% else %}
        <span style="color: #dc3545; font-weight: bold;">❌ {{ verification_status|title }}</span>
    {% endif %}<br>
    
    <strong>Your Permissions Include:</strong><br>
    <ul style="margin: 10px 0; padding-left: 20px;">
        {% for permission in permissions_summary %}
            <li>{{ permission }}</li>
        {% endfor %}
    </ul>
    
    {% if can_access_admin %}
        <strong>Admin Access:</strong> <span style="color: #28a745;">✅ Granted</span> - You can access the admin interface<br>
        <strong>Admin URL:</strong> <a href="{{ admin_url }}" style="color: #ff5d15;">{{ admin_url }}</a>
    {% else %}
        <strong>Admin Access:</strong> <span style="color: #6c757d;">❌ Limited</span> - Contact administrator if you need admin access
    {% endif %}
</div>

<!-- Security Notice -->
{% if temporary_password %}
<div class="warning-box">
    <strong>🔒 Important Security Notice:</strong><br>
    • Your temporary password is secure and does not expire<br>
    • You can change your password at any time through your profile settings<br>
    • Use a strong password with at least 8 characters, including uppercase, lowercase, numbers, and symbols<br>
    • Never share your login credentials with anyone<br>
    • If you forget your password, use the "Forgot Password" link on the login page
</div>
{% endif %}

<!-- Next Steps Section -->
<div class="content-text">
    <strong>📋 Next Steps to Get Started:</strong>
</div>

<div class="steps-container">
    <div class="step-item">
        <div class="step-number">1</div>
        <div class="step-content">
            <strong>First Login</strong><br>
            <a href="{{ login_url }}" style="color: #ff5d15;">Click here to log in</a> using your credentials above
            {% if temporary_password %}<br><small>You can change your password anytime in your profile settings</small>{% endif %}
        </div>
    </div>
    
    <div class="step-item">
        <div class="step-number">2</div>
        <div class="step-content">
            <strong>Complete Your Profile</strong><br>
            Add your bio, qualifications, specializations, and contact information<br>
            <small>This helps students and colleagues learn about your expertise</small>
        </div>
    </div>
    
    <div class="step-item">
        <div class="step-number">3</div>
        <div class="step-content">
            <strong>Explore Your Dashboard</strong><br>
            Familiarize yourself with the instructor tools and course management features<br>
            <small>Access course creation, student management, and analytics</small>
        </div>
    </div>
    
    {% if can_access_admin %}
    <div class="step-item">
        <div class="step-number">4</div>
        <div class="step-content">
            <strong>Access Admin Interface</strong><br>
            <a href="{{ admin_url }}" style="color: #ff5d15;">Visit the admin panel</a> for advanced management features<br>
            <small>Manage courses, users, and system settings</small>
        </div>
    </div>
    {% endif %}
</div>

<!-- Password Security Guidelines -->
<div class="content-text">
    <strong>🛡️ Password Security Guidelines:</strong>
</div>

<div class="info-box">
    When creating your new password, ensure it includes:<br>
    • At least 8 characters in length<br>
    • Mix of uppercase and lowercase letters<br>
    • At least one number<br>
    • At least one special character (!@#$%^&*)<br>
    • Avoid common words or personal information<br>
    • Don't reuse passwords from other accounts
</div>

<!-- Support Information -->
<div class="content-text">
    <strong>🆘 Need Help?</strong>
</div>

<div class="info-box">
    <strong>Technical Support:</strong> {{ support_email }}<br>
    <strong>Instructor Resources:</strong> <a href="{{ resources_url }}" style="color: #ff5d15;">{{ resources_url }}</a><br>
    <strong>Training Materials:</strong> Available in your instructor dashboard<br>
    <strong>Community Forum:</strong> Connect with other YITP instructors
</div>

<div class="content-text">
    We're here to support you every step of the way. Welcome to the YITP family, and thank you for your commitment to empowering young minds!
</div>

<div class="cta-container">
    <a href="{{ login_url }}" class="cta-button">
        🚀 Start Your Instructor Journey
    </a>
</div>

<!-- Additional CSS for steps -->
<style>
    .steps-container {
        margin: 20px 0;
    }
    
    .step-item {
        display: flex;
        align-items: flex-start;
        margin: 15px 0;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #ff5d15;
    }
    
    .step-number {
        background-color: #ff5d15;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 15px;
        flex-shrink: 0;
    }
    
    .step-content {
        flex: 1;
    }
    
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        border-left: 4px solid #ffc107;
    }
</style>
{% endblock %}
