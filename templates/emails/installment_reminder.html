{% extends "emails/base_email.html" %}

{% block title %}Installment Payment Reminder - YITP{% endblock %}

{% block content %}
<div class="greeting">
    Hello {{ user.get_full_name|default:user.username }},
</div>

<div class="content-text">
    This is a friendly reminder about your upcoming installment payment for the <strong>{{ course.title }}</strong> course.
</div>

<!-- Payment Status Box -->
<div class="info-box">
    <h3 style="margin-top: 0; color: #1a2e53;">📋 Payment Status</h3>
    <p><strong>Course:</strong> {{ course.title }}</p>
    <p><strong>First Installment:</strong> ✅ Paid (KES {{ first_payment_amount|floatformat:0 }})</p>
    <p><strong>Second Installment:</strong> ⏳ Due (KES {{ remaining_amount|floatformat:0 }})</p>
    <p><strong>Payment Due Date:</strong> {{ payment_due_date|date:"F d, Y" }}</p>
    <p><strong>Days Remaining:</strong> {{ days_remaining }} days</p>
</div>

<!-- Urgency Message -->
{% if days_remaining <= 7 %}
<div class="warning-box">
    <h4 style="margin-top: 0;">⚠️ Urgent: Payment Due Soon</h4>
    <p>Your second installment payment is due in <strong>{{ days_remaining }} days</strong>. Please complete your payment to maintain course access.</p>
</div>
{% elif days_remaining <= 14 %}
<div class="info-box">
    <h4 style="margin-top: 0;">📅 Payment Reminder</h4>
    <p>Your second installment payment is due in <strong>{{ days_remaining }} days</strong>. We recommend completing your payment soon to avoid any interruption in course access.</p>
</div>
{% endif %}

<div class="content-text">
    <strong>What happens next:</strong>
</div>

<ul style="margin: 20px 0; padding-left: 20px; color: #555;">
    <li>Complete your second installment payment before {{ payment_due_date|date:"F d, Y" }}</li>
    <li>Once verified, you'll have unlimited access to the complete course</li>
    <li>If payment is not received by the due date, your course access will be suspended</li>
</ul>

<!-- Payment Methods -->
<div class="info-box">
    <h4 style="margin-top: 0; color: #1a2e53;">💳 Payment Options</h4>
    <p><strong>M-Pesa:</strong> Pay Bill 4132847, Account: {{ user.profile.phone_number|default:user.username }}</p>
    <p><strong>PayPal:</strong> <a href="{{ paypal_payment_url }}" style="color: #ff5d15;">Complete Payment Online</a></p>
    <p><strong>Bank Transfer:</strong> Contact support for bank details</p>
</div>

<!-- Action Button -->
<div style="text-align: center; margin: 30px 0;">
    <a href="{{ payment_url }}" class="btn" style="background-color: #ff5d15; color: white; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: 600;">
        💳 Complete Payment Now
    </a>
</div>

<div class="content-text">
    If you have any questions or need assistance with your payment, please don't hesitate to contact our support team.
</div>

<!-- Support Information -->
<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff5d15;">
    <h4 style="color: #1a2e53; margin-top: 0;">💬 Need Help?</h4>
    <p style="margin-bottom: 10px;">Contact our support team:</p>
    <ul style="margin: 0; padding-left: 20px;">
        <li>Email: <a href="mailto:<EMAIL>" style="color: #ff5d15;"><EMAIL></a></li>
        <li>WhatsApp: <a href="https://wa.me/************" style="color: #ff5d15;">+254 722 646 958</a></li>
        <li>Phone: +254 722 646 958</li>
    </ul>
</div>

<div class="content-text">
    Thank you for choosing Youth Impact Training Programme. We're here to support your learning journey!
</div>

<div style="margin-top: 30px; color: #666; font-size: 14px;">
    Best regards,<br>
    <strong>The YITP Team</strong>
</div>
{% endblock %}
