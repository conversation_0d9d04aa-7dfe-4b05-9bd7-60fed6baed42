{% extends "emails/base_email.html" %}

{% block title %}Payment Verified Successfully - YITP{% endblock %}

{% block content %}
<div class="greeting">
    Congratulations {{ user.get_full_name|default:user.username }}!
</div>

<div class="content-text">
    Great news! Your payment for the <strong>{{ payment.course.title }}</strong> course has been successfully verified and confirmed.
</div>

<!-- Success Message -->
<div class="success-box">
    <div style="text-align: center; font-size: 48px; margin-bottom: 15px;">🎉</div>
    <h3 style="margin: 0 0 10px 0; color: #28a745; text-align: center;">Payment Verified!</h3>
    <p style="margin: 0; text-align: center;">Your course access is now active and ready to use.</p>
</div>

<!-- Payment Details -->
<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e9ecef;">
    <h4 style="color: #1a2e53; margin-top: 0;">📋 Verified Payment Details</h4>
    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
        <span style="font-weight: 600; color: #1a2e53;">Reference Number:</span>
        <span><strong>{{ payment.reference_number }}</strong></span>
    </div>
    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
        <span style="font-weight: 600; color: #1a2e53;">Course:</span>
        <span>{{ payment.course.title }}</span>
    </div>
    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
        <span style="font-weight: 600; color: #1a2e53;">Amount Paid:</span>
        <span><strong>KES {{ payment.amount|floatformat:0 }}</strong></span>
    </div>
    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
        <span style="font-weight: 600; color: #1a2e53;">Payment Type:</span>
        <span>
            {% if payment.is_installment %}
                Installment {{ payment.installment_sequence }} of 2
            {% else %}
                Full Payment
            {% endif %}
        </span>
    </div>
    <div style="display: flex; justify-content: space-between; padding: 8px 0;">
        <span style="font-weight: 600; color: #1a2e53;">Verified On:</span>
        <span>{{ payment.confirmed_at|date:"F d, Y H:i" }}</span>
    </div>
</div>

<!-- Access Information -->
{% if payment.is_installment and payment.installment_sequence == 1 %}
<div class="info-box">
    <h4 style="margin-top: 0; color: #1a2e53;">📅 Installment Payment Access</h4>
    <p><strong>First Installment Verified:</strong> You now have 30 days of course access.</p>
    <p><strong>Access Expires:</strong> {{ user.profile.payment_expiration_date|date:"F d, Y" }}</p>
    <p><strong>Second Payment Due:</strong> Complete your remaining payment of KES {{ user.profile.remaining_installment_amount|floatformat:0 }} before the expiry date for unlimited access.</p>
</div>
{% else %}
<div class="success-box">
    <h4 style="margin-top: 0; color: #28a745;">🎓 Full Course Access Granted</h4>
    <p>You now have unlimited access to all course materials, assignments, and resources!</p>
</div>
{% endif %}

<!-- Next Steps -->
<div class="content-text">
    <strong>What you can do now:</strong>
</div>

<ul style="margin: 20px 0; padding-left: 20px; color: #555;">
    <li>Access your course dashboard and start learning immediately</li>
    <li>Download course materials and resources</li>
    <li>Participate in assignments and assessments</li>
    <li>Connect with instructors and fellow students</li>
    {% if payment.is_installment and payment.installment_sequence == 1 %}
    <li>Plan your second installment payment before {{ user.profile.payment_expiration_date|date:"F d, Y" }}</li>
    {% else %}
    <li>Work towards earning your course completion certificate</li>
    {% endif %}
</ul>

<!-- Action Buttons -->
<div style="text-align: center; margin: 30px 0;">
    <a href="{{ course_url }}" class="btn" style="background-color: #ff5d15; color: white; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: 600; margin: 0 10px;">
        🎓 Start Learning Now
    </a>
    <a href="{{ dashboard_url }}" class="btn" style="background-color: #1a2e53; color: white; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: 600; margin: 0 10px;">
        📊 View Dashboard
    </a>
</div>

<!-- Course Information -->
<div style="background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3;">
    <h4 style="color: #1a2e53; margin-top: 0;">📚 About Your Course</h4>
    <p><strong>{{ payment.course.title }}</strong></p>
    <p>{{ payment.course.description|truncatewords:30 }}</p>
    {% if payment.course.duration %}
    <p><strong>Duration:</strong> {{ payment.course.duration }}</p>
    {% endif %}
    {% if payment.course.instructor %}
    <p><strong>Instructor:</strong> {{ payment.course.instructor }}</p>
    {% endif %}
</div>

<!-- Support Information -->
<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff5d15;">
    <h4 style="color: #1a2e53; margin-top: 0;">💬 Learning Support</h4>
    <p style="margin-bottom: 10px;">Need help with your course? Our support team is here for you:</p>
    <ul style="margin: 0; padding-left: 20px;">
        <li>Email: <a href="mailto:<EMAIL>" style="color: #ff5d15;"><EMAIL></a></li>
        <li>WhatsApp: <a href="https://wa.me/254722646958" style="color: #ff5d15;">+254 722 646 958</a></li>
        <li>Course Forum: Available in your course dashboard</li>
    </ul>
</div>

<div class="content-text">
    Welcome to the Youth Impact Training Programme community! We're excited to support your learning journey and help you achieve your goals.
</div>

<div style="margin-top: 30px; color: #666; font-size: 14px;">
    Best wishes for your learning success,<br>
    <strong>The YITP Team</strong>
</div>
{% endblock %}
