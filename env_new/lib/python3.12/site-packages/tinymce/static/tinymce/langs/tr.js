tinymce.addI18n("tr",{"Redo":"<PERSON><PERSON>","Undo":"<PERSON><PERSON> al","Cut":"<PERSON><PERSON>","Copy":"<PERSON><PERSON><PERSON>","Paste":"Yap\u0131\u015ft\u0131r","Select all":"T\xfcm\xfcn\xfc se\xe7","New document":"Yeni dok\xfcman","Ok":"Tamam","Cancel":"\u0130ptal","Visual aids":"G\xf6rsel ara\xe7lar","Bold":"Kal\u0131n","Italic":"\u0130talik","Underline":"Alt\u0131 \xe7izili","Strikethrough":"\xdcst\xfc \xe7izgili","Superscript":"\xdcst simge","Subscript":"Alt simge","Clear formatting":"Bi\xe7imi temizle","Remove":"Kald\u0131r","Align left":"<PERSON>a hizala","Align center":"<PERSON><PERSON><PERSON>","Align right":"Sa\u011fa hizala","No alignment":"Hizalama yok","Justify":"\u0130ki yana yasla","Bullet list":"S\u0131ras\u0131z liste","Numbered list":"S\u0131ral\u0131 liste","Decrease indent":"Girintiyi azalt","Increase indent":"Girintiyi art\u0131r","Close":"Kapat","Formats":"Bi\xe7imler","Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.":"Taray\u0131c\u0131n\u0131z panoya direk eri\u015fimi desteklemiyor. L\xfctfen Ctrl+X/C/V klavye k\u0131sayollar\u0131n\u0131 kullan\u0131n.","Headings":"Ba\u015fl\u0131klar","Heading 1":"Ba\u015fl\u0131k 1","Heading 2":"Ba\u015fl\u0131k 2","Heading 3":"Ba\u015fl\u0131k 3","Heading 4":"Ba\u015fl\u0131k 4","Heading 5":"Ba\u015fl\u0131k 5","Heading 6":"Ba\u015fl\u0131k 6","Preformatted":"\xd6nceden bi\xe7imlendirilmi\u015f","Div":"Div","Pre":"Pre","Code":"Kod","Paragraph":"Paragraf","Blockquote":"Blockquote","Inline":"Sat\u0131r i\xe7i","Blocks":"Bloklar","Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.":"D\xfcz metin modunda yap\u0131\u015ft\u0131r. Bu se\xe7ene\u011fi kapatana kadar i\xe7erikler d\xfcz metin olarak yap\u0131\u015ft\u0131r\u0131l\u0131r.","Fonts":"Yaz\u0131 Tipleri","Font sizes":"Yaz\u0131 tipi boyutu","Class":"S\u0131n\u0131f","Browse for an image":"Bir resim aray\u0131n","OR":"VEYA","Drop an image here":"Buraya bir resim koyun","Upload":"Y\xfckle","Uploading image":"Resim y\xfckleniyor","Block":"Blok","Align":"Hizala","Default":"Varsay\u0131lan","Circle":"Daire","Disc":"Disk","Square":"Kare","Lower Alpha":"K\xfc\xe7\xfck Harf","Lower Greek":"K\xfc\xe7\xfck Yunan alfabesi","Lower Roman":"K\xfc\xe7\xfck Roman","Upper Alpha":"B\xfcy\xfck Harf","Upper Roman":"B\xfcy\xfck Roman","Anchor...":"\xc7apa...","Anchor":"\xc7apa","Name":"\u0130sim","ID":"ID","ID should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.":"ID bir harfle ba\u015flamal\u0131, ard\u0131ndan yaln\u0131zca harf, say\u0131, tire, nokta, iki nokta \xfcst \xfcste veya alt \xe7izgi gelmelidir.","You have unsaved changes are you sure you want to navigate away?":"Kaydedilmemi\u015f de\u011fi\u015fiklikler var, sayfadan ayr\u0131lmak istedi\u011finize emin misiniz?","Restore last draft":"Son tasla\u011f\u0131 kurtar","Special character...":"\xd6zel karakter...","Special Character":"\xd6zel karakter","Source code":"Kaynak kodu","Insert/Edit code sample":"Kod \xf6rne\u011fini Kaydet/D\xfczenle","Language":"Dil","Code sample...":"Kod \xf6rne\u011fi...","Left to right":"Soldan sa\u011fa","Right to left":"Sa\u011fdan sola","Title":"Ba\u015fl\u0131k","Fullscreen":"Tam ekran","Action":"\u0130\u015flem","Shortcut":"K\u0131sayol","Help":"Yard\u0131m","Address":"Adres","Focus to menubar":"Men\xfc \xe7ubu\u011funa odaklan","Focus to toolbar":"Ara\xe7 \xe7ubu\u011funa odaklan","Focus to element path":"Eleman yoluna odaklan","Focus to contextual toolbar":"Ba\u011flamsal ara\xe7 \xe7ubu\u011funa odaklan","Insert link (if link plugin activated)":"Link ekle (Link eklentisi aktif ise)","Save (if save plugin activated)":"Kaydet (Kay\u0131t eklentisi aktif ise)","Find (if searchreplace plugin activated)":"Bul (SearchReplace eklentisi aktif ise)","Plugins installed ({0}):":"Y\xfckl\xfc eklenti ({0}):","Premium plugins:":"Premium eklentileri","Learn more...":"Daha fazla bilgi edinin...","You are using {0}":"{0} kullan\u0131yorsun.","Plugins":"Eklentiler","Handy Shortcuts":"Kullan\u0131\u015fl\u0131 K\u0131sayollar","Horizontal line":"Yatay \xe7izgi","Insert/edit image":"Resim ekle/d\xfczenle","Alternative description":"Alternatif a\xe7\u0131klama","Accessibility":"Eri\u015filebilirlik","Image is decorative":"Resim dekoratif","Source":"Kaynak","Dimensions":"Boyutlar","Constrain proportions":"En - Boy oran\u0131n\u0131 koru","General":"Genel","Advanced":"Geli\u015fmi\u015f","Style":"Stil","Vertical space":"Dikey bo\u015fluk","Horizontal space":"Yatay bo\u015fluk","Border":"Kenar","Insert image":"Resim ekle","Image...":"Resim...","Image list":"Resim listesi","Resize":"Yeniden Boyutland\u0131r","Insert date/time":"Tarih / Zaman ekle","Date/time":"Tarih/zaman","Insert/edit link":"Ba\u011flant\u0131 ekle/d\xfczenle","Text to display":"G\xf6r\xfcnt\xfclenecek metin","Url":"Url","Open link in...":"Ba\u011flant\u0131y\u0131 a\xe7...","Current window":"Mevcut pencere","None":"Yok","New window":"Yeni pencere","Open link":"Linki a\xe7","Remove link":"Ba\u011flant\u0131y\u0131 kald\u0131r","Anchors":"\xc7apalar","Link...":"Ba\u011flant\u0131...","Paste or type a link":"Bir ba\u011flant\u0131 yap\u0131\u015ft\u0131r\u0131n ya da yaz\u0131n.","The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?":"Girdi\u011finiz URL bir e-posta adresi gibi g\xf6z\xfck\xfcyor. Gerekli olan mailto: \xf6nekini eklemek ister misiniz?","The URL you entered seems to be an external link. Do you want to add the required http:// prefix?":"Girdi\u011finiz URL bir d\u0131\u015f ba\u011flant\u0131 gibi g\xf6z\xfck\xfcyor. Gerekli olan http:// \xf6nekini eklemek ister misiniz?","The URL you entered seems to be an external link. Do you want to add the required https:// prefix?":"Girdi\u011finiz URL bir d\u0131\u015f ba\u011flant\u0131 gibi g\xf6z\xfck\xfcyor. Gerekli olan https:// \xf6nekini eklemek ister misiniz?","Link list":"Link listesi","Insert video":"Video ekle","Insert/edit video":"Video ekle/d\xfczenle","Insert/edit media":"Medya ekle/d\xfczenle","Alternative source":"Alternatif kaynak","Alternative source URL":"Alternatif kaynak URL","Media poster (Image URL)":"Medya posteri (Resim URL)","Paste your embed code below:":"Medya g\xf6mme kodunu buraya yap\u0131\u015ft\u0131r:","Embed":"G\xf6mme","Media...":"Medya...","Nonbreaking space":"B\xf6l\xfcnemez bo\u015fluk","Page break":"Sayfa sonu","Paste as text":"Metin olarak yap\u0131\u015ft\u0131r","Preview":"\xd6nizleme","Print":"Yazd\u0131r","Print...":"Yazd\u0131r...","Save":"Kaydet","Find":"Bul","Replace with":"Bununla de\u011fi\u015ftir","Replace":"De\u011fi\u015ftir","Replace all":"T\xfcm\xfcn\xfc de\u011fi\u015ftir","Previous":"Geri","Next":"\u0130leri","Find and Replace":"Bul ve De\u011fi\u015ftir","Find and replace...":"Bul ve de\u011fi\u015ftir...","Could not find the specified string.":"Belirtilen dizin bulunamad\u0131.","Match case":"B\xfcy\xfck / K\xfc\xe7\xfck harfe duyarl\u0131","Find whole words only":"Sadece t\xfcm kelimeyi ara","Find in selection":"Se\xe7im i\xe7inde bul","Insert table":"Tablo ekle","Table properties":"Tablo \xf6zellikleri","Delete table":"Tabloyu sil","Cell":"H\xfccre","Row":"Sat\u0131r","Column":"S\xfctun","Cell properties":"H\xfccre \xf6zellikleri","Merge cells":"H\xfccreleri birle\u015ftir","Split cell":"H\xfccreleri ay\u0131r","Insert row before":"\xd6ncesine yeni sat\u0131r ekle","Insert row after":"Sonras\u0131na yeni sat\u0131r ekle","Delete row":"Sat\u0131r\u0131 sil","Row properties":"Sat\u0131r \xf6zellikleri","Cut row":"Sat\u0131r\u0131 kes","Cut column":"S\xfctunu kes","Copy row":"Sat\u0131r\u0131 kopyala","Copy column":"S\xfctunu kopyala","Paste row before":"\xd6ncesine sat\u0131r yap\u0131\u015ft\u0131r","Paste column before":"S\xfctun\xfc \xf6ncesine yap\u0131\u015ft\u0131r","Paste row after":"Sonras\u0131na sat\u0131r  yap\u0131\u015ft\u0131r","Paste column after":"S\xfctun\xfc sonras\u0131na yap\u0131\u015ft\u0131r","Insert column before":"\xd6ncesine yeni s\xfctun ekle","Insert column after":"Sonras\u0131na yeni s\xfctun ekle","Delete column":"S\xfctunu sil","Cols":"S\xfctunlar","Rows":"Sat\u0131rlar","Width":"Geni\u015flik","Height":"Y\xfckseklik","Cell spacing":"H\xfccre aral\u0131\u011f\u0131","Cell padding":"H\xfccre i\xe7 bo\u015flu\u011fu","Row clipboard actions":"Sat\u0131r panosu eylemleri","Column clipboard actions":"S\xfctun panosu eylemleri","Table styles":"Tablo stilleri","Cell styles":"H\xfccre stilleri","Column header":"S\xfct\xfcn ba\u015fl\u0131\u011f\u0131","Row header":"Sat\u0131r ba\u015fl\u0131\u011f\u0131","Table caption":"Tablo altyaz\u0131s\u0131","Caption":"Ba\u015fl\u0131k","Show caption":"Ba\u015fl\u0131\u011f\u0131 g\xf6ster","Left":"Sol","Center":"Orta","Right":"Sa\u011f","Cell type":"H\xfccre tipi","Scope":"Kapsam","Alignment":"Hizalama","Horizontal align":"Yatay hizalama","Vertical align":"Dikey hizalama","Top":"\xdcst","Middle":"Orta","Bottom":"Alt","Header cell":"Ba\u015fl\u0131k h\xfccresi","Row group":"Sat\u0131r grubu","Column group":"S\xfctun grubu","Row type":"Sat\u0131r tipi","Header":"Header","Body":"G\xf6vde","Footer":"Footer","Border color":"Kenarl\u0131k Rengi","Solid":"Kat\u0131","Dotted":"Noktal\u0131","Dashed":"Kesikli","Double":"\xc7ift","Groove":"Oluk","Ridge":"\xc7\u0131k\u0131nt\u0131","Inset":"\u0130\xe7 metin","Outset":"D\u0131\u015f Metin","Hidden":"Gizli","Insert template...":"\u015eablon ekle...","Templates":"\u015eablonlar","Template":"Tema","Insert Template":"\u015eablon Ekle","Text color":"Yaz\u0131 rengi","Background color":"Arkaplan rengi","Custom...":"\xd6zel...","Custom color":"\xd6zel Renk","No color":"Renk Yok","Remove color":"Rengi kald\u0131r","Show blocks":"Bloklar\u0131 g\xf6r\xfcnt\xfcle","Show invisible characters":"G\xf6r\xfcnmez karakterleri g\xf6ster","Word count":"Kelime say\u0131s\u0131","Count":"Say\u0131m","Document":"Belge","Selection":"Se\xe7im","Words":"S\xf6zc\xfck","Words: {0}":"Kelime: {0}","{0} words":"{0} kelime","File":"Dosya","Edit":"D\xfczenle","Insert":"Ekle","View":"G\xf6r\xfcnt\xfcle","Format":"Bi\xe7im","Table":"Tablo","Tools":"Ara\xe7lar","Powered by {0}":"Sa\u011flay\u0131c\u0131 {0}","Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help":"Zengin Metin Alan\u0131. Men\xfc i\xe7in ALT-F9 k\u0131sayolunu kullan\u0131n. Ara\xe7 \xe7ubu\u011fu i\xe7in ALT-F10 k\u0131sayolunu kullan\u0131n. Yard\u0131m i\xe7in ALT-0 k\u0131sayolunu kullan\u0131n.","Image title":"Resim ba\u015fl\u0131\u011f\u0131","Border width":"Kenar geni\u015fli\u011fi","Border style":"Kenar sitili","Error":"Hata","Warn":"Uyar\u0131","Valid":"Ge\xe7erli","To open the popup, press Shift+Enter":"Popup'\u0131 a\xe7mak i\xe7in Shift+Enter'a bas\u0131n","Rich Text Area":"Zengin Metin Alan\u0131","Rich Text Area. Press ALT-0 for help.":"Zengin Metin Alan\u0131. Yard\u0131m i\xe7in Alt-0'a bas\u0131n.","System Font":"Sistem Yaz\u0131 Tipi","Failed to upload image: {0}":"Resim y\xfcklenemedi: {0}","Failed to load plugin: {0} from url {1}":"Eklenti y\xfcklenemedi: {1} url\u2019sinden {0}","Failed to load plugin url: {0}":"Url eklentisi y\xfcklenemedi: {0}","Failed to initialize plugin: {0}":"Eklenti ba\u015flat\u0131lamad\u0131: {0}","example":"\xf6rnek","Search":"Ara","All":"T\xfcm\xfc","Currency":"Para birimi","Text":"Metin","Quotations":"Al\u0131nt\u0131","Mathematical":"Matematik","Extended Latin":"Uzat\u0131lm\u0131\u015f Latin","Symbols":"Semboller","Arrows":"Oklar","User Defined":"Kullan\u0131c\u0131 Tan\u0131ml\u0131","dollar sign":"dolar i\u015fareti","currency sign":"para birimi i\u015fareti","euro-currency sign":"euro para birimi i\u015fareti","colon sign":"colon i\u015fareti","cruzeiro sign":"cruzeiro i\u015fareti","french franc sign":"frans\u0131z frang\u0131 i\u015fareti","lira sign":"lira i\u015fareti","mill sign":"mill i\u015fareti","naira sign":"naira i\u015fareti","peseta sign":"peseta i\u015fareti","rupee sign":"rupi i\u015fareti","won sign":"won i\u015fareti","new sheqel sign":"yeni \u015fekel i\u015fareti","dong sign":"dong i\u015fareti","kip sign":"kip i\u015fareti","tugrik sign":"tugrik i\u015fareti","drachma sign":"drahma i\u015fareti","german penny symbol":"alman kuru\u015f sembol\xfc","peso sign":"peso i\u015fareti","guarani sign":"guarani i\u015fareti","austral sign":"austral i\u015fareti","hryvnia sign":"hrivniya i\u015fareti","cedi sign":"cedi i\u015fareti","livre tournois sign":"livre tournois i\u015fareti","spesmilo sign":"spesmilo i\u015fareti","tenge sign":"tenge i\u015fareti","indian rupee sign":"hindistan rupisi i\u015fareti","turkish lira sign":"t\xfcrk liras\u0131 i\u015fareti","nordic mark sign":"nordic i\u015fareti","manat sign":"manat i\u015fareti","ruble sign":"ruble i\u015fareti","yen character":"yen karakteri","yuan character":"yuan karakteri","yuan character, in hong kong and taiwan":"yuan karakteri, hong kong ve tayvan'da kullan\u0131lan","yen/yuan character variant one":"yen/yuan karakter de\u011fi\u015fkeni","Emojis":"Emojiler","Emojis...":"Emojiler...","Loading emojis...":"Emojiler y\xfckleniyor...","Could not load emojis":"Emojiler y\xfcklenemedi","People":"\u0130nsan","Animals and Nature":"Hayvanlar ve Do\u011fa","Food and Drink":"Yiyecek ve \u0130\xe7ecek","Activity":"Etkinlik","Travel and Places":"Gezi ve Yerler","Objects":"Nesneler","Flags":"Bayraklar","Characters":"Karakter","Characters (no spaces)":"Karakter (bo\u015fluksuz)","{0} characters":"{0} karakter","Error: Form submit field collision.":"Hata: Form g\xf6nderme alan\u0131 \xe7at\u0131\u015fmas\u0131.","Error: No form element found.":"Hata: Form eleman\u0131 bulunamad\u0131.","Color swatch":"Renk \xf6rne\u011fi","Color Picker":"Renk Se\xe7ici","Invalid hex color code: {0}":"Ge\xe7ersiz hex renk kodu: {0}","Invalid input":"Ge\xe7ersiz input","R":"K","Red component":"K\u0131rm\u0131z\u0131 par\xe7a","G":"Y","Green component":"Ye\u015fil par\xe7a","B":"M","Blue component":"Mavi par\xe7a","#":"#","Hex color code":"Hex renk kodu","Range 0 to 255":"0 ile 255 aral\u0131\u011f\u0131","Turquoise":"Turkuaz","Green":"Ye\u015fil","Blue":"Mavi","Purple":"Mor","Navy Blue":"Lacivert","Dark Turquoise":"Koyu Turkuaz","Dark Green":"Koyu Ye\u015fil","Medium Blue":"Donuk Mavi","Medium Purple":"Orta Mor","Midnight Blue":"Gece Yar\u0131s\u0131 Mavisi","Yellow":"Sar\u0131","Orange":"Turuncu","Red":"K\u0131rm\u0131z\u0131","Light Gray":"A\xe7\u0131k Gri","Gray":"Gri","Dark Yellow":"Koyu Sar\u0131","Dark Orange":"Koyu Turuncu","Dark Red":"Koyu K\u0131rm\u0131z\u0131","Medium Gray":"Orta Gri","Dark Gray":"Koyu Gri","Light Green":"A\xe7\u0131k Ye\u015fil","Light Yellow":"A\xe7\u0131k Sar\u0131","Light Red":"A\xe7\u0131k K\u0131rm\u0131z\u0131","Light Purple":"A\xe7\u0131k Mor","Light Blue":"A\xe7\u0131k Mavi","Dark Purple":"Koyu Mor","Dark Blue":"Lacivert","Black":"Siyah","White":"Beyaz","Switch to or from fullscreen mode":"Tam ekran moduna ge\xe7 veya \xe7\u0131k","Open help dialog":"Yard\u0131m penceresini a\xe7","history":"ge\xe7mi\u015f","styles":"stiller","formatting":"bi\xe7imlendirme","alignment":"hizalanma","indentation":"girinti","Font":"Yaz\u0131 Tipi","Size":"Boyut","More...":"Devam\u0131...","Select...":"Se\xe7...","Preferences":"Tercihler","Yes":"Evet","No":"Hay\u0131r","Keyboard Navigation":"Klavye Tu\u015flar\u0131","Version":"S\xfcr\xfcm","Code view":"Kod g\xf6r\xfcn\xfcm\xfc","Open popup menu for split buttons":"\xc7ok i\u015flevli butonlar i\xe7in a\xe7\u0131l\u0131r men\xfc a\xe7","List Properties":"\xd6zellikleri listele","List properties...":"Liste \xf6zellikleri","Start list at number":"Listeyi \u015fu say\u0131dan ba\u015flat","Line height":"Sat\u0131r y\xfcksekli\u011fi","Dropped file type is not supported":"S\xfcr\xfcklenen dosya tipi desteklenmiyor","Loading...":"Y\xfckleniyor...","ImageProxy HTTP error: Rejected request":"ImageProxy HTTP hatas\u0131: \u0130stek reddedildi","ImageProxy HTTP error: Could not find Image Proxy":"ImageProxy HTTP hatas\u0131: G\xf6rsel Proxy bulunamad\u0131","ImageProxy HTTP error: Incorrect Image Proxy URL":"ImageProxy HTTP hatas\u0131: Ge\xe7ersiz G\xf6rsel Proxy URL'i","ImageProxy HTTP error: Unknown ImageProxy error":"ImageProxy HTTP hatas\u0131: Bilinmeyen ImageProxy hatas\u0131"});