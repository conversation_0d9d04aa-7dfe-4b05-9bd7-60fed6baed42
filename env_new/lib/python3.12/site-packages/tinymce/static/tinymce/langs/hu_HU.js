tinymce.addI18n("hu_HU",{"Redo":"Ism\xe9t","Undo":"<PERSON><PERSON><PERSON>von\xe1s","Cut":"Kiv\xe1g\xe1s","Copy":"M\xe1sol\xe1s","Paste":"<PERSON><PERSON><PERSON>t\xe9s","Select all":"Minden kijel\xf6l\xe9se","New document":"\xdaj dokumentum","Ok":"Rendben","Cancel":"M\xe9gse","Visual aids":"Vizu\xe1lis seg\xe9deszk\xf6z\xf6k","Bold":"F\xe9lk\xf6v\xe9r","Italic":"D\u0151lt","Underline":"Al\xe1h\xfazott","Strikethrough":"\xc1th\xfazott","Superscript":"Fels\u0151 index","Subscript":"Als\xf3 index","Clear formatting":"Form\xe1z\xe1s t\xf6rl\xe9se","Remove":"Elt\xe1vol\xedt\xe1s","Align left":"Balra igaz\xedt\xe1s","Align center":"K\xf6z\xe9pre igaz\xedt\xe1s","Align right":"Jobbra igaz\xedt\xe1s","No alignment":"Igaz\xedt\xe1s n\xe9lk\xfcl","Justify":"Sorkiz\xe1rt","Bullet list":"Listajeles lista","Numbered list":"Sz\xe1mozott lista","Decrease indent":"Beh\xfaz\xe1s cs\xf6kkent\xe9se","Increase indent":"Beh\xfaz\xe1s n\xf6vel\xe9se","Close":"Bez\xe1r\xe1s","Formats":"Form\xe1tumok","Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.":"A b\xf6ng\xe9sz\u0151d nem t\xe1mogatja a k\xf6zvetlen hozz\xe1f\xe9r\xe9st a v\xe1g\xf3laphoz. K\xe9rlek, haszn\xe1ld a Ctrl+X/C/V billenty\u0171ket.","Headings":"C\xedmsorok","Heading 1":"1. c\xedmsor","Heading 2":"2. c\xedmsor","Heading 3":"3. c\xedmsor","Heading 4":"4. c\xedmsor","Heading 5":"5. c\xedmsor","Heading 6":"6. c\xedmsor","Preformatted":"El\u0151form\xe1zott","Div":"Div","Pre":"Pre","Code":"K\xf3d","Paragraph":"Bekezd\xe9s","Blockquote":"Id\xe9zetblokk","Inline":"Foly\xf3 sz\xf6veg","Blocks":"Blokkok","Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.":"Beilleszt\xe9s form\xe1zatlan sz\xf6vegk\xe9nt. A tartalom mostant\xf3l form\xe1zatlan sz\xf6vegk\xe9nt lesz beillesztve, am\xedg nem kapcsolod ki ezt az opci\xf3t.","Fonts":"Bet\u0171t\xedpusok","Font sizes":"Bet\u0171m\xe9ret","Class":"Oszt\xe1ly","Browse for an image":"K\xe9p keres\xe9se tall\xf3z\xe1ssal","OR":"VAGY","Drop an image here":"H\xfazz ide egy k\xe9pet","Upload":"Felt\xf6lt\xe9s","Uploading image":"K\xe9p felt\xf6lt\xe9se","Block":"Blokk","Align":"Igaz\xedt\xe1s","Default":"Alap\xe9rtelmezett","Circle":"K\xf6r","Disc":"Pont","Square":"N\xe9gyzet","Lower Alpha":"Kisbet\u0171s","Lower Greek":"Kisbet\u0171s g\xf6r\xf6g","Lower Roman":"Kisbet\u0171s r\xf3mai sz\xe1mos","Upper Alpha":"Nagybet\u0171s","Upper Roman":"Nagybet\u0171s r\xf3mai sz\xe1mos","Anchor...":"Horgony...","Anchor":"Horgony","Name":"N\xe9v","ID":"ID","ID should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.":"Az ID bet\u0171vel kezd\u0151dj\xf6n, \xe9s csak bet\u0171ket, sz\xe1mokat, k\xf6t\u0151jelet, pontot, kett\u0151spontot vagy alulvon\xe1st tartalmazzon.","You have unsaved changes are you sure you want to navigate away?":"Nem mentett m\xf3dos\xedt\xe1said vannak, biztosan el akarsz navig\xe1lni?","Restore last draft":"Utols\xf3 piszkozat vissza\xe1ll\xedt\xe1sa","Special character...":"Speci\xe1lis karakter...","Special Character":"Speci\xe1lis karakter","Source code":"Forr\xe1sk\xf3d","Insert/Edit code sample":"K\xf3dminta besz\xfar\xe1sa/szerkeszt\xe9se","Language":"Nyelv","Code sample...":"K\xf3dminta...","Left to right":"Balr\xf3l jobbra","Right to left":"Jobbr\xf3l balra","Title":"C\xedm","Fullscreen":"Teljes k\xe9perny\u0151","Action":"M\u0171velet","Shortcut":"Billenty\u0171kombin\xe1ci\xf3","Help":"S\xfag\xf3","Address":"C\xedm","Focus to menubar":"F\xf3kusz a men\xfcre","Focus to toolbar":"F\xf3kusz az eszk\xf6zt\xe1rra","Focus to element path":"F\xf3kusz az elem el\xe9r\xe9si \xfatj\xe1ra","Focus to contextual toolbar":"F\xf3kusz a k\xf6rnyezetf\xfcgg\u0151 eszk\xf6zt\xe1rra","Insert link (if link plugin activated)":"Hivatkoz\xe1s besz\xfar\xe1sa (ha a hivatkoz\xe1s be\xe9p\xfcl\u0151 modul enged\xe9lyezett)","Save (if save plugin activated)":"Ment\xe9s (ha a ment\xe9s be\xe9p\xfcl\u0151 modul enged\xe9lyezett)","Find (if searchreplace plugin activated)":"Keres\xe9s (ha a keres\xe9s \xe9s csere be\xe9p\xfcl\u0151 modul enged\xe9lyezett)","Plugins installed ({0}):":"Telep\xedtett be\xe9p\xfcl\u0151 modulok ({0}):","Premium plugins:":"Pr\xe9mium be\xe9p\xfcl\u0151 modulok:","Learn more...":"Tudj meg t\xf6bbet...","You are using {0}":"Haszn\xe1latban: {0}","Plugins":"Be\xe9p\xfcl\u0151 modulok","Handy Shortcuts":"Hasznos billenty\u0171parancsok","Horizontal line":"V\xedzszintes vonal","Insert/edit image":"K\xe9p beilleszt\xe9se/szerkeszt\xe9se","Alternative description":"Alternat\xedv le\xedr\xe1s","Accessibility":"Akad\xe1lymentes\xedt\xe9s","Image is decorative":"Dekor\xe1ci\xf3s k\xe9p","Source":"Forr\xe1s","Dimensions":"M\xe9retek","Constrain proportions":"M\xe9retar\xe1ny","General":"\xc1ltal\xe1nos","Advanced":"Speci\xe1lis","Style":"St\xedlus","Vertical space":"T\xe9rk\xf6z f\xfcgg\u0151legesen","Horizontal space":"T\xe9rk\xf6z v\xedzszintesen","Border":"Szeg\xe9ly","Insert image":"K\xe9p beilleszt\xe9se","Image...":"K\xe9p...","Image list":"K\xe9plista","Resize":"\xc1tm\xe9retez\xe9s","Insert date/time":"D\xe1tum/id\u0151 beilleszt\xe9se","Date/time":"D\xe1tum/id\u0151","Insert/edit link":"Hivatkoz\xe1s besz\xfar\xe1sa/szerkeszt\xe9se","Text to display":"Megjelen\xedtend\u0151 sz\xf6veg","Url":"Webc\xedm","Open link in...":"Hivatkoz\xe1s megnyit\xe1sa...","Current window":"Jelenlegi ablak","None":"Nincs","New window":"\xdaj ablak","Open link":"Hivatkoz\xe1s megnyit\xe1sa","Remove link":"Hivatkoz\xe1s t\xf6rl\xe9se","Anchors":"Horgonyok","Link...":"Hivatkoz\xe1s...","Paste or type a link":"Hivatkoz\xe1s be\xedr\xe1sa vagy beilleszt\xe9se","The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?":"A megadott URL e-mail-c\xedmnek t\u0171nik. Szeretn\xe9d hozz\xe1adni a sz\xfcks\xe9ges mailto: el\u0151tagot?","The URL you entered seems to be an external link. Do you want to add the required http:// prefix?":"A megadott URL k\xfcls\u0151 c\xedmnek t\u0171nik. Szeretn\xe9d hozz\xe1adni a sz\xfcks\xe9ges http:// el\u0151tagot?","The URL you entered seems to be an external link. Do you want to add the required https:// prefix?":"Az URL amit megadt\xe1l k\xfcls\u0151 hivatkoz\xe1s. Szeretn\xe9d https:// el\u0151taggal megnyitni?","Link list":"Hivatkoz\xe1slista","Insert video":"Vide\xf3 beilleszt\xe9se","Insert/edit video":"Vide\xf3 beilleszt\xe9se/szerkeszt\xe9se","Insert/edit media":"M\xe9dia beilleszt\xe9se/szerkeszt\xe9se","Alternative source":"Alternat\xedv forr\xe1s","Alternative source URL":"Alternat\xedv forr\xe1s URL","Media poster (Image URL)":"M\xe9dia poszter (k\xe9p URL)","Paste your embed code below:":"Illeszd be a be\xe1gyaz\xe1si k\xf3dot al\xe1bb:","Embed":"Be\xe1gyaz\xe1s","Media...":"M\xe9dia...","Nonbreaking space":"Nem t\xf6rhet\u0151 sz\xf3k\xf6z","Page break":"Oldalt\xf6r\xe9s","Paste as text":"Beilleszt\xe9s sz\xf6vegk\xe9nt","Preview":"El\u0151n\xe9zet","Print":"Nyomtat\xe1s","Print...":"Nyomtat\xe1s...","Save":"Ment\xe9s","Find":"Keres\xe9s","Replace with":"Csere erre:","Replace":"Csere","Replace all":"Az \xf6sszes cser\xe9je","Previous":"El\u0151z\u0151","Next":"K\xf6vetkez\u0151","Find and Replace":"Keres\xe9s \xe9s csere","Find and replace...":"Keres\xe9s \xe9s csere...","Could not find the specified string.":"A be\xedrt kifejez\xe9s nem tal\xe1lhat\xf3.","Match case":"Kis- \xe9s nagybet\u0171k megk\xfcl\xf6nb\xf6ztet\xe9se","Find whole words only":"Csak teljes szavak keres\xe9se","Find in selection":"Keres\xe9s a kiv\xe1laszt\xe1sban","Insert table":"T\xe1bl\xe1zat beilleszt\xe9se","Table properties":"T\xe1bl\xe1zat tulajdons\xe1gai","Delete table":"T\xe1bl\xe1zat t\xf6rl\xe9se","Cell":"Cella","Row":"Sor","Column":"Oszlop","Cell properties":"Cella tulajdons\xe1gai","Merge cells":"Cell\xe1k egyes\xedt\xe9se","Split cell":"Cell\xe1k sz\xe9tv\xe1laszt\xe1sa","Insert row before":"Sor besz\xfar\xe1sa el\xe9","Insert row after":"Sor besz\xfar\xe1sa m\xf6g\xe9","Delete row":"Sor t\xf6rl\xe9se","Row properties":"Sor tulajdons\xe1gai","Cut row":"Sor kiv\xe1g\xe1sa","Cut column":"Oszlop kiv\xe1g\xe1sa","Copy row":"Sor m\xe1sol\xe1sa","Copy column":"Oszlop m\xe1sol\xe1sa","Paste row before":"Sor beilleszt\xe9se el\xe9","Paste column before":"Oszlop besz\xfar\xe1sa el\xe9","Paste row after":"Sor besz\xfar\xe1sa ut\xe1na","Paste column after":"Oszlop besz\xfar\xe1sa ut\xe1na","Insert column before":"Oszlop besz\xfar\xe1sa el\xe9","Insert column after":"Oszlop besz\xfar\xe1sa m\xf6g\xe9","Delete column":"Oszlop t\xf6rl\xe9se","Cols":"Oszlopok","Rows":"Sorok","Width":"Sz\xe9less\xe9g","Height":"Magass\xe1g","Cell spacing":"Cellat\xe1vols\xe1g","Cell padding":"Cellamarg\xf3","Row clipboard actions":"Sor v\xe1g\xf3lapi m\u0171veletek","Column clipboard actions":"Oszlop v\xe1g\xf3lapi m\u0171veletek","Table styles":"T\xe1bl\xe1zatst\xedlusok","Cell styles":"Cellast\xedlusok","Column header":"Oszlopfejl\xe9c","Row header":"Sorfejl\xe9c","Table caption":"T\xe1bl\xe1zatfelirat","Caption":"Felirat","Show caption":"Felirat megjelen\xedt\xe9se","Left":"Balra","Center":"K\xf6z\xe9pre","Right":"Jobbra","Cell type":"Cellat\xedpus","Scope":"Tartom\xe1ny","Alignment":"Igaz\xedt\xe1s","Horizontal align":"V\xedzszintes igaz\xedt\xe1s","Vertical align":"F\xfcgg\u0151leges igaz\xedt\xe1s","Top":"Fel\xfclre","Middle":"K\xf6z\xe9pre","Bottom":"Alulra","Header cell":"C\xedmsor cella","Row group":"Sorcsoport","Column group":"Oszlopcsoport","Row type":"Sort\xedpus","Header":"Fejl\xe9c","Body":"Sz\xf6vegt\xf6rzs","Footer":"L\xe1bl\xe9c","Border color":"Szeg\xe9lysz\xedn","Solid":"Szimpla","Dotted":"Pontozott","Dashed":"Szaggatott","Double":"Dupla","Groove":"Faragott","Ridge":"Dombor\xfa","Inset":"S\xfcllyesztett","Outset":"Kiemelt","Hidden":"Rejtett","Insert template...":"Sablon besz\xfar\xe1sa...","Templates":"Sablonok","Template":"Sablon","Insert Template":"Sablon besz\xfar\xe1sa","Text color":"Sz\xf6veg sz\xedne","Background color":"H\xe1tt\xe9rsz\xedn","Custom...":"Egy\xe9ni...","Custom color":"Egy\xe9ni sz\xedn","No color":"Nincs sz\xedn","Remove color":"Sz\xedn t\xf6rl\xe9se","Show blocks":"Blokkok mutat\xe1sa","Show invisible characters":"L\xe1thatatlan karakterek mutat\xe1sa","Word count":"Szavak sz\xe1ma","Count":"Sz\xe1m","Document":"Dokumentum","Selection":"Kiv\xe1laszt\xe1s","Words":"Szavak","Words: {0}":"Szavak: {0}","{0} words":"{0} sz\xf3","File":"F\xe1jl","Edit":"Szerkeszt\xe9s","Insert":"Besz\xfar\xe1s","View":"N\xe9zet","Format":"Form\xe1tum","Table":"T\xe1bl\xe1zat","Tools":"Eszk\xf6z\xf6k","Powered by {0}":"Szolg\xe1ltat\xf3: {0}","Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help":"Vizu\xe1lis szerkeszt\u0151 ter\xfclet. Nyomjon ALT-F9-et a men\xfch\xf6z. Nyomjon ALT-F10-et az eszk\xf6zt\xe1rhoz. Nyomjon ALT-0-t a s\xfag\xf3hoz","Image title":"K\xe9p c\xedme","Border width":"Szeg\xe9ly vastags\xe1ga","Border style":"Szeg\xe9ly st\xedlusa","Error":"Hiba","Warn":"Figyelmeztet\xe9s","Valid":"\xc9rv\xe9nyes","To open the popup, press Shift+Enter":"A felugr\xf3 ablak megnyit\xe1s\xe1hoz nyomja meg a Shift+Enter billenty\u0171t","Rich Text Area":"Szabadsz\xf6veges mez\u0151","Rich Text Area. Press ALT-0 for help.":"Vizu\xe1lis szerkeszt\u0151 ter\xfclet. Nyomjon ALT-0-t a s\xfag\xf3hoz.","System Font":"Rendszer bet\u0171t\xedpusa","Failed to upload image: {0}":"Nem siker\xfclt felt\xf6lteni a k\xe9pet: {0}","Failed to load plugin: {0} from url {1}":"Nem siker\xfclt bet\xf6lteni a be\xe9p\xfcl\u0151 modult: {0} err\u0151l a webc\xedmr\u0151l: {1}","Failed to load plugin url: {0}":"Nem siker\xfclt bet\xf6lteni a be\xe9p\xfcl\u0151 modul webc\xedm\xe9t: {0}","Failed to initialize plugin: {0}":"Nem siker\xfclt el\u0151k\xe9sz\xedteni a be\xe9p\xfcl\u0151 modult: {0}","example":"p\xe9lda","Search":"Keres\xe9s","All":"Minden","Currency":"P\xe9nznem","Text":"Sz\xf6veg","Quotations":"Id\xe9z\u0151jelek","Mathematical":"Matematikai","Extended Latin":"B\u0151v\xedtett latin","Symbols":"Szimb\xf3lumok","Arrows":"Nyilak","User Defined":"Felhaszn\xe1l\xf3 \xe1ltal meghat\xe1rozott","dollar sign":"doll\xe1r jel","currency sign":"valuta jel","euro-currency sign":"euro-valuta jel","colon sign":"kett\u0151spont","cruzeiro sign":"cruzeiro jel","french franc sign":"francia frank jel","lira sign":"l\xedra jel","mill sign":"mill jel","naira sign":"naira jel","peseta sign":"peseta jel","rupee sign":"r\xfapia jel","won sign":"won jel","new sheqel sign":"\xfaj s\xe9kel jel","dong sign":"dong jel","kip sign":"kip jel","tugrik sign":"tugrik jel","drachma sign":"drachma jel","german penny symbol":"n\xe9met penny jel","peso sign":"peso jel","guarani sign":"guarani jel","austral sign":"austral jel","hryvnia sign":"hrivnya jel","cedi sign":"cedi jel","livre tournois sign":"livre tournois jel","spesmilo sign":"spesmilo jel","tenge sign":"tenge jel","indian rupee sign":"r\xfapia jel","turkish lira sign":"t\xf6r\xf6k l\xedra jel","nordic mark sign":"\xe9szaki m\xe1rka jel","manat sign":"manat jel","ruble sign":"rubel jel","yen character":"jen karakter","yuan character":"j\xfcan karakter","yuan character, in hong kong and taiwan":"hongkongi \xe9s tajvani j\xfcan karakter","yen/yuan character variant one":"jen/j\xfcan karaktervari\xe1ns","Emojis":"Hangulatjelek","Emojis...":"Hangulatjelek...","Loading emojis...":"Hangulatjelek bet\xf6lt\xe9se...","Could not load emojis":"A hangulatjeleket nem siker\xfclt bet\xf6lteni","People":"Emberek","Animals and Nature":"\xc1llatok \xe9s term\xe9szet","Food and Drink":"\xc9tel, ital","Activity":"Tev\xe9kenys\xe9gek","Travel and Places":"Utaz\xe1s \xe9s helyek","Objects":"T\xe1rgyak","Flags":"Z\xe1szl\xf3k","Characters":"Karakterek","Characters (no spaces)":"Karakterek (sz\xf3k\xf6z\xf6k n\xe9lk\xfcl)","{0} characters":"{0} karakter","Error: Form submit field collision.":"Hiba: \xdctk\xf6z\xe9s t\xf6rt\xe9nt az \u0171rlap elk\xfcld\xe9sekor.","Error: No form element found.":"Hiba: Nem tal\xe1lhat\xf3 \u0171rlap elem.","Color swatch":"Sz\xednpaletta","Color Picker":"Sz\xednv\xe1laszt\xf3","Invalid hex color code: {0}":"\xc9rv\xe9nytelen hexadecim\xe1lis sz\xednk\xf3d: {0}","Invalid input":"\xc9rv\xe9nytelen bemenet","R":"R","Red component":"Piros komponens","G":"G","Green component":"Z\xf6ld komponens","B":"B","Blue component":"K\xe9k komponens","#":"#","Hex color code":"Hexadecim\xe1lis sz\xednk\xf3d","Range 0 to 255":"0-t\xf3l 255-ig","Turquoise":"T\xfcrkiz","Green":"Z\xf6ld","Blue":"K\xe9k","Purple":"Lila","Navy Blue":"Tengerk\xe9k","Dark Turquoise":"S\xf6t\xe9tt\xfcrkiz","Dark Green":"S\xf6t\xe9tz\xf6ld","Medium Blue":"Kir\xe1lyk\xe9k","Medium Purple":"K\xf6z\xe9plila","Midnight Blue":"\xc9jf\xe9lk\xe9k","Yellow":"S\xe1rga","Orange":"Narancss\xe1rga","Red":"Piros","Light Gray":"Vil\xe1gossz\xfcrke","Gray":"Sz\xfcrke","Dark Yellow":"S\xf6t\xe9ts\xe1rga","Dark Orange":"S\xf6t\xe9t narancss\xe1rga","Dark Red":"S\xf6t\xe9tv\xf6r\xf6s","Medium Gray":"K\xf6z\xe9psz\xfcrke","Dark Gray":"S\xf6t\xe9tsz\xfcrke","Light Green":"Vil\xe1gosz\xf6ld","Light Yellow":"Vil\xe1goss\xe1rga","Light Red":"Vil\xe1gospiros","Light Purple":"Vil\xe1goslila","Light Blue":"Vil\xe1gosk\xe9k","Dark Purple":"S\xf6t\xe9tlila","Dark Blue":"S\xf6t\xe9tk\xe9k","Black":"Fekete","White":"Feh\xe9r","Switch to or from fullscreen mode":"Teljes vagy norm\xe1l k\xe9perny\u0151s m\xf3dra v\xe1lt\xe1s","Open help dialog":"S\xfag\xf3ablak megnyit\xe1sa","history":"el\u0151zm\xe9nyek","styles":"st\xedlusok","formatting":"form\xe1z\xe1s","alignment":"igaz\xedt\xe1s","indentation":"beh\xfaz\xe1s","Font":"Bet\u0171t\xedpus","Size":"M\xe9ret","More...":"Tov\xe1bbiak...","Select...":"V\xe1lasszon...","Preferences":"Be\xe1ll\xedt\xe1sok","Yes":"Igen","No":"Nem","Keyboard Navigation":"Billenty\u0171zettel val\xf3 navig\xe1l\xe1s","Version":"Verzi\xf3","Code view":"K\xf3dn\xe9zet","Open popup menu for split buttons":"Felugr\xf3 men\xfc megnyit\xe1sa az osztott gombokhoz","List Properties":"Lista tulajdons\xe1gai","List properties...":"Lista tulajdons\xe1gai...","Start list at number":"Lista kezd\xe9se ett\u0151l a sz\xe1mt\xf3l","Line height":"Sor magass\xe1ga","Dropped file type is not supported":"Nem t\xe1mogatott f\xe1jlt\xedpus","Loading...":"Bet\xf6lt\xe9s...","ImageProxy HTTP error: Rejected request":"ImageProxy HTTP hiba: k\xe9r\xe9s elutas\xedtva","ImageProxy HTTP error: Could not find Image Proxy":"ImageProxy HTTP hiba: nincs ilyen Image Proxy","ImageProxy HTTP error: Incorrect Image Proxy URL":"ImageProxy HTTP hiba: helytelen Image Proxy URL","ImageProxy HTTP error: Unknown ImageProxy error":"ImageProxy HTTP hiba: ismeretlen ImageProxy hiba"});