tinymce.addI18n("ko_KR",{"Redo":"\ub2e4\uc2dc \uc2e4\ud589","Undo":"\uc2e4\ud589 \ucde8\uc18c","Cut":"\uc798\ub77c\ub0b4\uae30","Copy":"\ubcf5\uc0ac","Paste":"\ubd99\uc5ec\ub123\uae30","Select all":"\uc804\uccb4\uc120\ud0dd","New document":"\uc0c8 \ubb38\uc11c","Ok":"\ud655\uc778","Cancel":"\ucde8\uc18c","Visual aids":"\ud45c\uc758 \ud14c\ub450\ub9ac\ub97c \uc810\uc120\uc73c\ub85c \ud45c\uc2dc","Bold":"\uad75\uac8c","Italic":"\uae30\uc6b8\uc784\uaf34","Underline":"\ubc11\uc904","Strikethrough":"\ucde8\uc18c\uc120","Superscript":"\uc704 \ucca8\uc790","Subscript":"\uc544\ub798 \ucca8\uc790","Clear formatting":"\uc11c\uc2dd \uc9c0\uc6b0\uae30","Remove":"\uc81c\uac70","Align left":"\uc67c\ucabd \uc815\ub82c","Align center":"\uc911\uc559 \uc815\ub82c","Align right":"\uc624\ub978\ucabd \uc815\ub82c","No alignment":"\uc815\ub82c \uc5c6\uc74c","Justify":"\uc591\ucabd \uc815\ub82c","Bullet list":"\uae00\uba38\ub9ac \uae30\ud638 \ubaa9\ub85d","Numbered list":"\ubc88\ud638 \ub9e4\uae30\uae30 \ubaa9\ub85d","Decrease indent":"\ub0b4\uc5b4\uc4f0\uae30","Increase indent":"\ub4e4\uc5ec\uc4f0\uae30","Close":"\ub2eb\uae30","Formats":"\uc11c\uc2dd","Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.":"\ube0c\ub77c\uc6b0\uc800\uac00 \ud074\ub9bd\ubcf4\ub4dc \uc811\uadfc\uc744 \uc9c0\uc6d0\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4. Ctrl+X/C/V \ub2e8\ucd95\ud0a4\ub97c \uc774\uc6a9\ud574\uc8fc\uc138\uc694.","Headings":"\uc81c\ubaa9","Heading 1":"\uc81c\ubaa9 1","Heading 2":"\uc81c\ubaa9 2","Heading 3":"\uc81c\ubaa9 3","Heading 4":"\uc81c\ubaa9 4","Heading 5":"\uc81c\ubaa9 5","Heading 6":"\uc81c\ubaa9 6","Preformatted":"\uc11c\uc2dd \ubbf8\uc124\uc815","Div":"Div","Pre":"Pre","Code":"\ucf54\ub4dc","Paragraph":"\ub2e8\ub77d","Blockquote":"\uc778\uc6a9\ubb38","Inline":"\uc778\ub77c\uc778","Blocks":"\ube14\ub85d","Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.":"\ubd99\uc5ec\ub123\uae30\uac00 \ud604\uc7ac \uc77c\ubc18 \ud14d\uc2a4\ud2b8 \ubaa8\ub4dc\uc785\ub2c8\ub2e4. \uc774 \uc635\uc158\uc744 \ud574\uc81c\ud560 \ub54c\uae4c\uc9c0 \uc77c\ubc18 \ud14d\uc2a4\ud2b8\ub85c \ubd99\uc5ec\ub123\uc2b5\ub2c8\ub2e4.","Fonts":"\uae00\uaf34","Font sizes":"\uae00\uaf34 \ud06c\uae30","Class":"\ud074\ub798\uc2a4","Browse for an image":"\uc774\ubbf8\uc9c0 \ucc3e\uae30","OR":"\ub610\ub294","Drop an image here":"\uc5ec\uae30\ub85c \uc774\ubbf8\uc9c0\ub97c \ub04c\uc5b4\uc624\uc138\uc694","Upload":"\uc5c5\ub85c\ub4dc","Uploading image":"\uc774\ubbf8\uc9c0 \uc5c5\ub85c\ub4dc \uc911","Block":"\ube14\ub85d","Align":"\uc815\ub82c","Default":"\uae30\ubcf8\uac12","Circle":"\ub3d9\uadf8\ub77c\ubbf8","Disc":"\ub514\uc2a4\ud06c","Square":"\ub124\ubaa8","Lower Alpha":"\uc54c\ud30c\ubcb3 \uc18c\ubb38\uc790","Lower Greek":"\uadf8\ub9ac\uc2a4\uc5b4 \uc18c\ubb38\uc790","Lower Roman":"\ub85c\ub9c8\uc790 \uc18c\ubb38\uc790","Upper Alpha":"\uc54c\ud30c\ubcb3 \ub300\ubb38\uc790","Upper Roman":"\ub85c\ub9c8\uc790 \ub300\ubb38\uc790","Anchor...":"\uc575\ucee4...","Anchor":"\ub9c1\ud06c \uc9c0\uc810","Name":"\uc774\ub984","ID":"ID","ID should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.":"ID\ub294 \ubb38\uc790\ub85c \uc2dc\uc791\ud574\uc57c \ud558\uba70, \uadf8 \ub2e4\uc74c\uc5d0\ub294 \ubb38\uc790, \uc22b\uc790, \ub300\uc2dc, \uad6c\ub450\uc810, \ucf5c\ub860, \ubc11\uc904 \ubb38\uc790\uac00 \uc62c \uc218 \uc788\uc2b5\ub2c8\ub2e4.","You have unsaved changes are you sure you want to navigate away?":"\uc800\uc7a5\ud558\uc9c0 \uc54a\uc740 \uc815\ubcf4\uac00 \uc788\uc2b5\ub2c8\ub2e4. \uc774 \ud398\uc774\uc9c0\ub97c \ub098\uac00\uc2dc\uaca0\uc2b5\ub2c8\uae4c?","Restore last draft":"\ub9c8\uc9c0\ub9c9 \ucd08\uc548 \ubcf5\uc6d0","Special character...":"\ud2b9\uc218 \ubb38\uc790...","Special Character":"\ud2b9\uc218 \ubb38\uc790","Source code":"\uc18c\uc2a4\ucf54\ub4dc","Insert/Edit code sample":"\ucf54\ub4dc \uc0d8\ud50c \uc0bd\uc785/\ud3b8\uc9d1","Language":"\uc5b8\uc5b4","Code sample...":"\ucf54\ub4dc \uc0d8\ud50c...","Left to right":"\uc67c\ucabd\uc5d0\uc11c \uc624\ub978\ucabd","Right to left":"\uc624\ub978\ucabd\uc5d0\uc11c \uc67c\ucabd","Title":"\uc81c\ubaa9","Fullscreen":"\uc804\uccb4 \ud654\uba74","Action":"\uc791\uc5c5","Shortcut":"\ubc14\ub85c\uac00\uae30","Help":"\ub3c4\uc6c0\ub9d0","Address":"\uc8fc\uc18c","Focus to menubar":"\uba54\ub274\ubc14\uc5d0 \uac15\uc870\ud45c\uc2dc","Focus to toolbar":"\ud234\ubc14\uc5d0 \uac15\uc870\ud45c\uc2dc","Focus to element path":"\uc694\uc18c \uacbd\ub85c\uc5d0 \uac15\uc870\ud45c\uc2dc","Focus to contextual toolbar":"\ucee8\ud14d\uc2a4\ud2b8 \ud234\ubc14\uc5d0 \uac15\uc870\ud45c\uc2dc","Insert link (if link plugin activated)":"\ub9c1\ud06c \uc0bd\uc785 (link \ud50c\ub7ec\uadf8\uc778\uc774 \ud65c\uc131\ud654\ub41c \uacbd\uc6b0)","Save (if save plugin activated)":"\uc800\uc7a5 (save \ud50c\ub7ec\uadf8\uc778\uc774 \ud65c\uc131\ud654\ub41c \uacbd\uc6b0)","Find (if searchreplace plugin activated)":"\ucc3e\uae30 (searchreplace \ud50c\ub7ec\uadf8\uc778\uc774 \ud65c\uc131\ud654\ub41c \uacbd\uc6b0)","Plugins installed ({0}):":"\uc124\uce58\ub41c \ud50c\ub7ec\uadf8\uc778({0}):","Premium plugins:":"\ud504\ub9ac\ubbf8\uc5c4 \ud50c\ub7ec\uadf8\uc778:","Learn more...":"\uc880 \ub354 \uc0b4\ud3b4\ubcf4\uae30...","You are using {0}":"{0} \uc0ac\uc6a9 \uc911","Plugins":"\ud50c\ub7ec\uadf8\uc778","Handy Shortcuts":"\uc720\uc6a9\ud55c \ub2e8\ucd95\ud0a4","Horizontal line":"\uc218\ud3c9\uc120","Insert/edit image":"\uc774\ubbf8\uc9c0 \uc0bd\uc785/\ud3b8\uc9d1","Alternative description":"\ub300\uccb4 \uc124\uba85\ubb38","Accessibility":"\uc811\uadfc\uc131","Image is decorative":"\uc774\ubbf8\uc9c0 \uc7a5\uc2dd \uac00\ub2a5","Source":"\uc18c\uc2a4","Dimensions":"\ud06c\uae30","Constrain proportions":"\ube44\uc728 \uace0\uc815","General":"\uc77c\ubc18","Advanced":"\uc0c1\uc138","Style":"\uc2a4\ud0c0\uc77c","Vertical space":"\uc0c1\ud558 \uc5ec\ubc31","Horizontal space":"\uc88c\uc6b0 \uc5ec\ubc31","Border":"\ud14c\ub450\ub9ac","Insert image":"\uc774\ubbf8\uc9c0 \uc0bd\uc785","Image...":"\uc774\ubbf8\uc9c0...","Image list":"\uc774\ubbf8\uc9c0 \ubaa9\ub85d","Resize":"\ud06c\uae30 \uc870\uc808","Insert date/time":"\ub0a0\uc9dc/\uc2dc\uac04 \uc0bd\uc785","Date/time":"\ub0a0\uc9dc/\uc2dc\uac04","Insert/edit link":"\ub9c1\ud06c \uc0bd\uc785/\ud3b8\uc9d1","Text to display":"\ud45c\uc2dc\ud560 \ud14d\uc2a4\ud2b8","Url":"URL","Open link in...":"...\uc5d0\uc11c \ub9c1\ud06c \uc5f4\uae30","Current window":"\ud604\uc7ac \ucc3d","None":"\uc5c6\uc74c","New window":"\uc0c8 \ucc3d","Open link":"\ub9c1\ud06c \uc5f4\uae30","Remove link":"\ub9c1\ud06c \uc81c\uac70","Anchors":"\uc575\ucee4","Link...":"\ub9c1\ud06c...","Paste or type a link":"\ub9c1\ud06c\ub97c \ubd99\uc5ec\ub123\uac70\ub098 \uc785\ub825\ud558\uc2ed\uc2dc\uc624.","The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?":'\uc785\ub825\ud558\uc2e0 URL\uc774 \uc774\uba54\uc77c \uc8fc\uc18c\uc778 \uac83 \uac19\uc2b5\ub2c8\ub2e4. "mailto:" \uc811\ub450\uc0ac\ub97c \ucd94\uac00\ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?',"The URL you entered seems to be an external link. Do you want to add the required http:// prefix?":'\uc785\ub825\ud558\uc2e0 URL\uc774 \uc678\ubd80 \ub9c1\ud06c\uc778 \uac83 \uac19\uc2b5\ub2c8\ub2e4. "http://" \uc811\ub450\uc0ac\ub97c \ucd94\uac00\ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?',"The URL you entered seems to be an external link. Do you want to add the required https:// prefix?":'\uc785\ub825\ud558\uc2e0 URL\uc774 \uc678\ubd80 \ub9c1\ud06c\uc778 \uac83 \uac19\uc2b5\ub2c8\ub2e4. "https://" \uc811\ub450\uc0ac\ub97c \ucd94\uac00\ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?',"Link list":"\ub9c1\ud06c \ubaa9\ub85d","Insert video":"\ube44\ub514\uc624 \uc0bd\uc785","Insert/edit video":"\ube44\ub514\uc624 \uc0bd\uc785/\ud3b8\uc9d1","Insert/edit media":"\ubbf8\ub514\uc5b4 \uc0bd\uc785/\ud3b8\uc9d1","Alternative source":"\ub300\uccb4 \uc18c\uc2a4","Alternative source URL":"\ub300\uccb4 \uc18c\uc2a4 URL","Media poster (Image URL)":"\ubbf8\ub514\uc5b4 \ud3ec\uc2a4\ud130 (\uc774\ubbf8\uc9c0 URL)","Paste your embed code below:":"\uc0bd\uc785\ud560 \ucf54\ub4dc\ub97c \uc544\ub798\uc5d0 \ubd99\uc5ec \ub123\uc5b4\uc8fc\uc138\uc694.","Embed":"\uc0bd\uc785","Media...":"\ubbf8\ub514\uc5b4...","Nonbreaking space":"\ub744\uc5b4\uc4f0\uae30","Page break":"\ud398\uc774\uc9c0 \uad6c\ubd84\uc790","Paste as text":"\ud14d\uc2a4\ud2b8\ub85c \ubd99\uc5ec\ub123\uae30","Preview":"\ubbf8\ub9ac \ubcf4\uae30","Print":"\uc778\uc1c4","Print...":"\uc778\uc1c4...","Save":"\uc800\uc7a5","Find":"\ucc3e\uae30","Replace with":"\ub2e4\uc74c\uc73c\ub85c \ubc14\uafb8\uae30:","Replace":"\ubc14\uafb8\uae30","Replace all":"\ubaa8\ub450 \ubc14\uafb8\uae30","Previous":"\uc774\uc804","Next":"\ub2e4\uc74c","Find and Replace":"\ucc3e\uae30 \ubc0f \ubc14\uafb8\uae30","Find and replace...":"\ucc3e\uae30 \ubc0f \ubc14\uafb8\uae30...","Could not find the specified string.":"\uc9c0\uc815\ud55c \ubb38\uc790\ub97c \ucc3e\uc744 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.","Match case":"\ub300/\uc18c\ubb38\uc790 \uad6c\ubd84","Find whole words only":"\ubaa8\ub450 \uc77c\uce58\ud558\ub294 \ub2e8\uc5b4 \ucc3e\uae30","Find in selection":"\uc120\ud0dd\ub41c \ubd80\ubd84\uc5d0\uc11c \uac80\uc0c9","Insert table":"\ud45c \uc0bd\uc785","Table properties":"\ud45c \uc18d\uc131","Delete table":"\ud45c \uc0ad\uc81c","Cell":"\uc140","Row":"\ud589","Column":"\uc5f4","Cell properties":"\uc140 \uc18d\uc131","Merge cells":"\uc140 \ubcd1\ud569","Split cell":"\uc140 \ubd84\ud560","Insert row before":"\uc774\uc804\uc5d0 \ud589 \uc0bd\uc785","Insert row after":"\ub2e4\uc74c\uc5d0 \ud589 \uc0bd\uc785","Delete row":"\ud589 \uc0ad\uc81c","Row properties":"\ud589 \uc18d\uc131","Cut row":"\ud589 \uc798\ub77c\ub0b4\uae30","Cut column":"\uc5f4 \uc798\ub77c\ub0b4\uae30","Copy row":"\ud589 \ubcf5\uc0ac","Copy column":"\uc5f4 \ubcf5\uc0ac","Paste row before":"\uc774\uc804\uc5d0 \ud589 \ubd99\uc5ec\ub123\uae30","Paste column before":"\uc774\uc804\uc5d0 \uc5f4 \ubd99\uc5ec\ub123\uae30","Paste row after":"\ub2e4\uc74c\uc5d0 \ud589 \ubd99\uc5ec\ub123\uae30","Paste column after":"\ub2e4\uc74c\uc5d0 \uc5f4 \ubd99\uc5ec\ub123\uae30","Insert column before":"\uc774\uc804\uc5d0 \uc5f4 \uc0bd\uc785","Insert column after":"\ub2e4\uc74c\uc5d0 \uc5f4 \uc0bd\uc785","Delete column":"\uc5f4 \uc0ad\uc81c","Cols":"\uc5f4 \uc218","Rows":"\ud589 \uc218","Width":"\ub108\ube44","Height":"\ub192\uc774","Cell spacing":"\uc140 \uac04\uaca9","Cell padding":"\uc140 \uc548\ucabd \uc5ec\ubc31","Row clipboard actions":"\ud589 \ud074\ub9bd\ubcf4\ub4dc \ub3d9\uc791","Column clipboard actions":"\uc5f4 \ud074\ub9bd\ubcf4\ub4dc \ub3d9\uc791","Table styles":"\ud45c \ubaa8\uc591","Cell styles":"\uc140 \ubaa8\uc591","Column header":"\uc5f4 \uc81c\ubaa9","Row header":"\ud589 \uc81c\ubaa9","Table caption":"\ud45c \ucea1\uc158","Caption":"\ucea1\uc158","Show caption":"\ucea1\uc158 \ud45c\uc2dc","Left":"\uc67c\ucabd \ub9de\ucda4","Center":"\uac00\uc6b4\ub370 \ub9de\ucda4","Right":"\uc624\ub978\ucabd \ub9de\ucda4","Cell type":"\uc140 \uc720\ud615","Scope":"\ubc94\uc704","Alignment":"\uc815\ub82c","Horizontal align":"\uc218\ud3c9 \uc815\ub82c","Vertical align":"\uc218\uc9c1 \uc815\ub82c","Top":"\uc704\ucabd \ub9de\ucda4","Middle":"\uac00\uc6b4\ub370 \ub9de\ucda4","Bottom":"\uc544\ub798 \ub9de\ucda4","Header cell":"\ud5e4\ub354 \uc140","Row group":"\ud589 \uadf8\ub8f9","Column group":"\uc5f4 \uadf8\ub8f9","Row type":"\ud589 \uc720\ud615","Header":"\uc81c\ubaa9","Body":"\ubcf8\ubb38","Footer":"\ud478\ud130","Border color":"\ud14c\ub450\ub9ac \uc0c9","Solid":"\uc2e4\uc120","Dotted":"\uc810\uc120","Dashed":"\ud30c\uc120","Double":"\uc774\uc911 \uc2e4\uc120","Groove":"\uc785\uccb4 \ud14c\ub450\ub9ac","Ridge":"\ub3cc\ucd9c \ud14c\ub450\ub9ac","Inset":"\uc140 \ud568\ubab0","Outset":"\uc140 \ub3cc\ucd9c","Hidden":"\uc228\uae40","Insert template...":"\ud15c\ud50c\ub9bf \uc0bd\uc785...","Templates":"\ud15c\ud50c\ub9bf","Template":"\ud15c\ud50c\ub9bf","Insert Template":"\ud15c\ud50c\ub9bf \uc0bd\uc785","Text color":"\uae00\uc790 \uc0c9","Background color":"\ubc30\uacbd \uc0c9","Custom...":"\uc0ac\uc6a9\uc790 \uc9c0\uc815...","Custom color":"\uc0ac\uc6a9\uc790 \uc9c0\uc815 \uc0c9","No color":"\uc0c9 \uc5c6\uc74c","Remove color":"\uc0c9 \uc81c\uac70","Show blocks":"\ube14\ub85d \ud45c\uc2dc","Show invisible characters":"\ube44\ud45c\uc2dc \ubb38\uc790 \ud45c\uc2dc","Word count":"\ubb38\uc790 \uc218","Count":"\uac1c\uc218","Document":"\ubb38\uc11c","Selection":"\uc120\ud0dd","Words":"\ub2e8\uc5b4 \uc218","Words: {0}":"\ub2e8\uc5b4 \uc218: {0}","{0} words":"{0}\uac1c\uc758 \ub2e8\uc5b4","File":"\ud30c\uc77c","Edit":"\ud3b8\uc9d1","Insert":"\uc0bd\uc785","View":"\ubcf4\uae30","Format":"\uc11c\uc2dd","Table":"\ud45c","Tools":"\ub3c4\uad6c","Powered by {0}":"{0}\uc5d0\uc11c \uc9c0\uc6d0","Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help":"\uc11c\uc2dd \uc788\ub294 \ud14d\uc2a4\ud2b8 \uc601\uc5ed. ALT-F9\ub97c \ub204\ub974\uba74 \uba54\ub274, ALT-F10\uc744 \ub204\ub974\uba74 \ud234\ubc14, ALT-0\uc744 \ub204\ub974\uba74 \ub3c4\uc6c0\ub9d0\uc744 \ubcfc \uc218 \uc788\uc2b5\ub2c8\ub2e4.","Image title":"\uc774\ubbf8\uc9c0 \uc81c\ubaa9","Border width":"\ud14c\ub450\ub9ac \ub450\uaed8","Border style":"\ud14c\ub450\ub9ac \uc2a4\ud0c0\uc77c","Error":"\uc624\ub958","Warn":"\uacbd\uace0","Valid":"\uc720\ud6a8\ud568","To open the popup, press Shift+Enter":"\ud31d\uc5c5\uc744 \uc5f4\ub824\uba74 Shift+Enter\ub97c \ub204\ub974\uc2ed\uc2dc\uc624.","Rich Text Area":"\uc11c\uc2dd \ud14d\uc2a4\ud2b8 \uc601\uc5ed","Rich Text Area. Press ALT-0 for help.":"\uc11c\uc2dd \uc788\ub294 \ud14d\uc2a4\ud2b8 \uc601\uc5ed. ALT-0\uc744 \ub204\ub974\uba74 \ub3c4\uc6c0\ub9d0\uc744 \ubcfc \uc218 \uc788\uc2b5\ub2c8\ub2e4.","System Font":"\uc2dc\uc2a4\ud15c \uae00\uaf34","Failed to upload image: {0}":"\uc774\ubbf8\uc9c0{0}\uc744(\ub97c) \uc5c5\ub85c\ub4dc \ud558\uc9c0 \ubabb\ud588\uc2b5\ub2c8\ub2e4.","Failed to load plugin: {0} from url {1}":"URL {1}\ub85c\ubd80\ud130 \ud50c\ub7ec\uadf8\uc778 {0}\uc744 \ubd88\ub7ec\uc624\uc9c0 \ubabb\ud588\uc2b5\ub2c8\ub2e4.","Failed to load plugin url: {0}":"\ud50c\ub7ec\uadf8\uc778 URL {0}\uc744 \ubd88\ub7ec\uc624\uc9c0 \ubabb\ud588\uc2b5\ub2c8\ub2e4.","Failed to initialize plugin: {0}":"\ud50c\ub7ec\uadf8\uc778 {0}\uc758 \ucd08\uae30\ud654\uac00 \uc2e4\ud328\ud588\uc2b5\ub2c8\ub2e4","example":"\uc608\uc81c","Search":"\uac80\uc0c9","All":"\ubaa8\ub450","Currency":"\ud1b5\ud654","Text":"\ud14d\uc2a4\ud2b8","Quotations":"\uc778\uc6a9\ubb38","Mathematical":"\uc218\ud559\uae30\ud638","Extended Latin":"\ud655\uc7a5 \ub77c\ud2f4\uc5b4","Symbols":"\uae30\ud638","Arrows":"\ud654\uc0b4\ud45c","User Defined":"\uc0ac\uc6a9\uc790 \uc815\uc758","dollar sign":"\ub2ec\ub7ec \uae30\ud638","currency sign":"\ud1b5\ud654 \uae30\ud638","euro-currency sign":"\uc720\ub85c\ud654 \uae30\ud638","colon sign":"\ucf5c\ub860 \uae30\ud638","cruzeiro sign":"\ud06c\ub8e8\uc81c\uc774\ub85c \uae30\ud638","french franc sign":"\ud504\ub791\uc2a4 \ud504\ub791 \uae30\ud638","lira sign":"\ub9ac\ub77c \uae30\ud638","mill sign":"\ubc00 \uae30\ud638","naira sign":"\ub098\uc774\ub77c \uae30\ud638","peseta sign":"\ud398\uc138\ud0c0 \uae30\ud638","rupee sign":"\ub8e8\ud53c \uae30\ud638","won sign":"\uc6d0 \uae30\ud638","new sheqel sign":"\ub274 \uc138\ucf08 \uae30\ud638","dong sign":"\ub3d9 \uae30\ud638","kip sign":"\ud0b5 \uae30\ud638","tugrik sign":"\ud22c\uadf8\ub9ac\ud06c \uae30\ud638","drachma sign":"\ub4dc\ub77c\ud06c\ub9c8 \uae30\ud638","german penny symbol":"\ub3c5\uc77c \ud398\ub2c8 \uae30\ud638","peso sign":"\ud398\uc18c \uae30\ud638","guarani sign":"\uacfc\ub77c\ub2c8 \uae30\ud638","austral sign":"\uc544\uc6b0\uc2a4\ud2b8\ub784 \uae30\ud638","hryvnia sign":"\uadf8\ub9ac\ube0c\ub098 \uae30\ud638","cedi sign":"\uc138\ub514 \uae30\ud638","livre tournois sign":"\ub9ac\ube0c\ub974 \ud2b8\ub974\ub204\uc544 \uae30\ud638","spesmilo sign":"\uc2a4\ud398\uc2a4\ubc00\ub85c \uae30\ud638","tenge sign":"\ud161\uac8c \uae30\ud638","indian rupee sign":"\uc778\ub3c4 \ub8e8\ud53c \uae30\ud638","turkish lira sign":"\ud130\ud0a4 \ub9ac\ub77c \uae30\ud638","nordic mark sign":"\ub178\ub974\ub515 \ub9c8\ub974\ud06c \uae30\ud638","manat sign":"\ub9c8\ub098\ud2b8 \uae30\ud638","ruble sign":"\ub8e8\ube14 \uae30\ud638","yen character":"\uc5d4 \uae30\ud638","yuan character":"\uc704\uc548 \uae30\ud638","yuan character, in hong kong and taiwan":"\ub300\ub9cc \uc704\uc548 \uae30\ud638","yen/yuan character variant one":"\uc5d4/\uc704\uc548 \ubb38\uc790 \ubcc0\ud615","Emojis":"\uc774\ubaa8\uc9c0","Emojis...":"\uc774\ubaa8\uc9c0...","Loading emojis...":"\uc774\ubaa8\uc9c0 \ubd88\ub7ec\uc624\ub294 \uc911...","Could not load emojis":"\uc774\ubaa8\uc9c0\ub97c \ubd88\ub7ec\uc62c \uc218 \uc5c6\uc2b5\ub2c8\ub2e4","People":"\uc0ac\ub78c","Animals and Nature":"\ub3d9\ubb3c\uacfc \uc790\uc5f0","Food and Drink":"\uc74c\uc2dd\uacfc \uc74c\ub8cc","Activity":"\ud65c\ub3d9","Travel and Places":"\uc5ec\ud589\uacfc \uc7a5\uc18c","Objects":"\ubb3c\uac74","Flags":"\uae43\ubc1c","Characters":"\ubb38\uc790 \uc218","Characters (no spaces)":"\ubb38\uc790 \uc218 (\uacf5\ubc31 \uc5c6\uc74c)","{0} characters":"{0} \ubb38\uc790","Error: Form submit field collision.":"\uc624\ub958: \uc591\uc2dd \uc81c\ucd9c \ud544\ub4dc \ubd88\uc77c\uce58","Error: No form element found.":"\uc624\ub958: \uc591\uc2dd \ud56d\ubaa9 \uc5c6\uc74c","Color swatch":"\uc0c9\uc0c1 \uacac\ubcf8","Color Picker":"\uc0c9 \uc120\ud0dd\uae30","Invalid hex color code: {0}":"\ubd80\uc801\uc808\ud55c 16\uc9c4\uc218 \uc0c9\uc0c1 \ucf54\ub4dc: {0}","Invalid input":"\ubd80\uc801\uc808\ud55c \uc785\ub825","R":"\ube68\uac15","Red component":"\uc801\uc0c9 \uc694\uc18c","G":"\ub179\uc0c9","Green component":"\ub179\uc0c9 \uc694\uc18c","B":"\ud30c\ub791","Blue component":"\uccad\uc0c9 \uc694\uc18c","#":"#","Hex color code":"16\uc9c4\uc218 \uc0c9\uc0c1 \ucf54\ub4dc","Range 0 to 255":"0\ubd80\ud130 255\uae4c\uc9c0\uc758 \ubc94\uc704","Turquoise":"\uccad\ub85d\uc0c9","Green":"\ucd08\ub85d\uc0c9","Blue":"\ud30c\ub780\uc0c9","Purple":"\ubcf4\ub77c\uc0c9","Navy Blue":"\ub0a8\uc0c9","Dark Turquoise":"\uc9c4\ud55c \uccad\ub85d\uc0c9","Dark Green":"\uc9c4\ud55c \ucd08\ub85d\uc0c9","Medium Blue":"\uc911\uac04 \ud30c\ub780\uc0c9","Medium Purple":"\uc911\uac04 \ubcf4\ub77c\uc0c9","Midnight Blue":"\uc9c4\ud55c \ud30c\ub780\uc0c9","Yellow":"\ub178\ub780\uc0c9","Orange":"\uc8fc\ud669\uc0c9","Red":"\ube68\uac04\uc0c9","Light Gray":"\ubc1d\uc740 \ud68c\uc0c9","Gray":"\ud68c\uc0c9","Dark Yellow":"\uc9c4\ud55c \ub178\ub780\uc0c9","Dark Orange":"\uc9c4\ud55c \uc8fc\ud669\uc0c9","Dark Red":"\uc9c4\ud55c \ube68\uac04\uc0c9","Medium Gray":"\uc911\uac04 \ud68c\uc0c9","Dark Gray":"\uc9c4\ud55c \ud68c\uc0c9","Light Green":"\ubc1d\uc740 \ub179\uc0c9","Light Yellow":"\ubc1d\uc740 \ub178\ub780\uc0c9","Light Red":"\ubc1d\uc740 \ube68\uac04\uc0c9","Light Purple":"\ubc1d\uc740 \ubcf4\ub77c\uc0c9","Light Blue":"\ubc1d\uc740 \ud30c\ub780\uc0c9","Dark Purple":"\uc9c4\ud55c \ubcf4\ub77c\uc0c9","Dark Blue":"\uc9c4\ud55c \ud30c\ub780\uc0c9","Black":"\uac80\uc740\uc0c9","White":"\ud770\uc0c9","Switch to or from fullscreen mode":"\uc804\uccb4 \ud654\uba74 \ubaa8\ub4dc \uc804\ud658","Open help dialog":"\ub3c4\uc6c0\ub9d0 \ub2e4\uc774\uc5bc\ub85c\uadf8 \uc5f4\uae30","history":"\uc774\ub825","styles":"\uc2a4\ud0c0\uc77c","formatting":"\uc11c\uc2dd","alignment":"\uc815\ub82c","indentation":"\ub4e4\uc5ec\uc4f0\uae30","Font":"\uae00\uaf34","Size":"\ud06c\uae30","More...":"\ub354 \ubcf4\uae30...","Select...":"\uc120\ud0dd...","Preferences":"\ud658\uacbd\uc124\uc815","Yes":"\ub124","No":"\uc544\ub2c8\uc624","Keyboard Navigation":"\ub2e8\ucd95\ud0a4","Version":"\ubc84\uc804","Code view":"\ucf54\ub4dc \ud45c\uc2dc","Open popup menu for split buttons":"\ubd84\ud560 \ubc84\ud2bc\uc73c\ub85c \ud31d\uc5c5 \uba54\ub274 \uc5f4\uae30","List Properties":"\ud56d\ubaa9 \uc18d\uc131","List properties...":"\ud56d\ubaa9 \uc18d\uc131...","Start list at number":"\ubc88\ud638 \ub9ac\uc2a4\ud2b8 \uc2dc\uc791","Line height":"\ud589 \ub192\uc774","Dropped file type is not supported":"\ub04c\uc5b4\ub2e4 \ub193\uc740 \ud30c\uc77c \ud615\uc2dd\uc744 \uc9c0\uc6d0\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4","Loading...":"\ubd88\ub7ec\uc624\ub294 \uc911...","ImageProxy HTTP error: Rejected request":"ImageProxy HTTP \uc624\ub958: \uc694\uccad \uac70\ubd80","ImageProxy HTTP error: Could not find Image Proxy":"ImageProxy HTTP \uc624\ub958: \uc774\ubbf8\uc9c0 \ud504\ub85d\uc2dc\ub97c \ucc3e\uc744 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4","ImageProxy HTTP error: Incorrect Image Proxy URL":"ImageProxy HTTP \uc624\ub958: \uc62c\ubc14\ub974\uc9c0 \uc54a\uc740 \uc774\ubbf8\uc9c0 \ud504\ub85d\uc2dc URL \uc8fc\uc18c","ImageProxy HTTP error: Unknown ImageProxy error":"ImageProxy HTTP \uc624\ub958: \uc54c \uc218 \uc5c6\ub294 \uc774\ubbf8\uc9c0 \ud504\ub85d\uc2dc \uc624\ub958"});