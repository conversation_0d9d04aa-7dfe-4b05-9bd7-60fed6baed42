# YITP Payment Automation Cron Jobs
# Add these to your server's crontab for automated payment management

# Check payment expiry and send warnings - Run daily at 9:00 AM
0 9 * * * cd /path/to/yitp && python manage.py check_payment_expiry --send-warnings

# Send installment reminders - Run daily at 10:00 AM
0 10 * * * cd /path/to/yitp && python manage.py send_renewal_reminders --type=installment

# Send renewal reminders - Run daily at 11:00 AM
0 11 * * * cd /path/to/yitp && python manage.py send_renewal_reminders --type=renewal

# Process installment due dates - Run daily at 8:00 AM
0 8 * * * cd /path/to/yitp && python manage.py process_installment_due --check-overdue

# Auto-expire overdue installments - Run daily at 2:00 AM
0 2 * * * cd /path/to/yitp && python manage.py process_installment_due --auto-expire

# Weekly comprehensive check - Run every Sunday at 6:00 AM
0 6 * * 0 cd /path/to/yitp && python manage.py check_payment_expiry --send-warnings && python manage.py send_renewal_reminders --type=all && python manage.py process_installment_due --check-overdue --auto-expire

# Installation Instructions:
# 1. Replace "/path/to/yitp" with your actual YITP project path
# 2. Ensure the virtual environment is activated in the cron commands if needed:
#    Example: cd /path/to/yitp && source env_new/bin/activate && python manage.py check_payment_expiry --send-warnings
# 3. Add these lines to your crontab using: crontab -e
# 4. Verify cron jobs are running with: crontab -l
# 5. Check cron logs for any issues: tail -f /var/log/cron

# Alternative: Django-Crontab Package
# If you prefer to manage cron jobs through Django settings, install django-crontab:
# pip install django-crontab
# 
# Then add to settings.py:
# CRONJOBS = [
#     ('0 9 * * *', 'payments.management.commands.check_payment_expiry', ['--send-warnings']),
#     ('0 10 * * *', 'payments.management.commands.send_renewal_reminders', ['--type=installment']),
#     ('0 11 * * *', 'payments.management.commands.send_renewal_reminders', ['--type=renewal']),
#     ('0 8 * * *', 'payments.management.commands.process_installment_due', ['--check-overdue']),
#     ('0 2 * * *', 'payments.management.commands.process_installment_due', ['--auto-expire']),
# ]
#
# Then run: python manage.py crontab add
