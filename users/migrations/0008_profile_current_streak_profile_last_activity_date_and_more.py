# Generated by Django 4.2.21 on 2025-07-11 00:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0007_comprehensive_payment_sponsorship_system'),
    ]

    operations = [
        migrations.AddField(
            model_name='profile',
            name='current_streak',
            field=models.IntegerField(default=0, help_text='Current consecutive days of learning activity'),
        ),
        migrations.AddField(
            model_name='profile',
            name='last_activity_date',
            field=models.DateField(blank=True, help_text='Date of last learning activity for streak calculation', null=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='longest_streak',
            field=models.IntegerField(default=0, help_text='Longest consecutive days of learning activity achieved'),
        ),
        migrations.AddField(
            model_name='profile',
            name='total_points',
            field=models.IntegerField(default=0, help_text='Total points earned by the user'),
        ),
    ]
