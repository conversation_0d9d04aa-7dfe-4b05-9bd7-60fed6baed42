# Generated by Django 4.2.21 on 2025-07-05 16:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('courses', '0001_initial'),
        ('users', '0006_profile_email_verified'),
    ]

    operations = [
        migrations.AddField(
            model_name='profile',
            name='installment_amount_paid',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Amount paid in first installment (50% of course price)', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='partial_payment_date',
            field=models.DateTimeField(blank=True, help_text='Date when partial payment (50%) was made', null=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='payment_expiration_date',
            field=models.DateTimeField(blank=True, help_text='Date when partial payment access expires (30 days from partial payment)', null=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='sponsorship_request',
            field=models.ForeignKey(blank=True, help_text="Linked sponsorship request if payment status is 'sponsorship'", null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sponsored_profile', to='users.sponsorshiprequest'),
        ),
        migrations.AddField(
            model_name='profile',
            name='total_course_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Total course price for installment tracking', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='sponsorshiprequest',
            name='approved_at',
            field=models.DateTimeField(blank=True, help_text='Date and time when sponsorship was approved', null=True),
        ),
        migrations.AddField(
            model_name='sponsorshiprequest',
            name='approved_by',
            field=models.ForeignKey(blank=True, help_text='Admin user who approved this request', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_sponsorships', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sponsorshiprequest',
            name='course',
            field=models.ForeignKey(blank=True, help_text='Course for which sponsorship is requested', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sponsorship_requests', to='courses.course'),
        ),
        migrations.AddField(
            model_name='sponsorshiprequest',
            name='rejection_reason',
            field=models.TextField(blank=True, help_text='Reason for rejection (if applicable)'),
        ),
        migrations.AlterField(
            model_name='profile',
            name='payment_method',
            field=models.CharField(blank=True, choices=[('mpesa', 'M-Pesa'), ('bank_transfer', 'Bank Transfer'), ('paypal', 'PayPal'), ('cash', 'Cash Payment'), ('installment', 'Installment Plan'), ('scholarship', 'Scholarship'), ('other', 'Other')], help_text='Method used for payment', max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='profile',
            name='payment_status',
            field=models.CharField(choices=[('unpaid', 'Unpaid'), ('pending', 'Pending Verification'), ('confirmed', 'Payment Confirmed'), ('expired', 'Payment Expired'), ('partially_paid', 'Partially Paid (Installment)'), ('sponsorship', 'Sponsored Access')], default='unpaid', help_text='Current payment status for YITP courses', max_length=20),
        ),
    ]
