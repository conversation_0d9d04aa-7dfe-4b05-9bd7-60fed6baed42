# Generated by Django 4.2.21 on 2025-07-11 18:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('users', '0008_profile_current_streak_profile_last_activity_date_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Specialization',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.Char<PERSON>ield(blank=True, help_text='CSS icon class', max_length=50)),
                ('color', models.Char<PERSON>ield(default='#ff5d15', help_text='Hex color code', max_length=7)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Specialization',
                'verbose_name_plural': 'Specializations',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='InstructorProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('instructor_role', models.CharField(choices=[('system_admin', 'System Administrator'), ('course_instructor', 'Course Instructor'), ('teaching_assistant', 'Teaching Assistant'), ('content_creator', 'Content Creator'), ('grader', 'Grader')], default='course_instructor', help_text='Primary instructor role in the system', max_length=30)),
                ('bio', models.TextField(blank=True, help_text='Professional biography and teaching philosophy')),
                ('qualifications', models.TextField(blank=True, help_text='Educational background and certifications')),
                ('years_experience', models.IntegerField(default=0, help_text='Years of teaching/training experience')),
                ('linkedin_url', models.URLField(blank=True, help_text='LinkedIn profile URL')),
                ('website_url', models.URLField(blank=True, help_text='Personal or professional website')),
                ('office_hours', models.TextField(blank=True, help_text='Available office hours for student consultations')),
                ('verification_status', models.CharField(choices=[('pending', 'Pending Verification'), ('verified', 'Verified'), ('rejected', 'Rejected')], default='pending', help_text='Instructor verification status', max_length=20)),
                ('is_active', models.BooleanField(default=True, help_text='Whether instructor is currently active')),
                ('can_create_courses', models.BooleanField(default=True, help_text='Permission to create new courses')),
                ('can_manage_assessments', models.BooleanField(default=True, help_text='Permission to create and manage quizzes/assessments')),
                ('can_view_analytics', models.BooleanField(default=True, help_text='Permission to view student analytics and progress')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('specializations', models.ManyToManyField(blank=True, help_text='Areas of expertise and specialization', to='users.specialization')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='instructor_profile', to=settings.AUTH_USER_MODEL)),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_instructors', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Instructor Profile',
                'verbose_name_plural': 'Instructor Profiles',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CourseInstructor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assignment_role', models.CharField(choices=[('primary_instructor', 'Primary Instructor'), ('co_instructor', 'Co-Instructor'), ('teaching_assistant', 'Teaching Assistant'), ('grader', 'Grader'), ('guest_lecturer', 'Guest Lecturer')], default='teaching_assistant', max_length=30)),
                ('can_edit_content', models.BooleanField(default=True)),
                ('can_manage_enrollments', models.BooleanField(default=True)),
                ('can_grade_assessments', models.BooleanField(default=True)),
                ('can_view_analytics', models.BooleanField(default=True)),
                ('can_communicate_students', models.BooleanField(default=True)),
                ('can_publish_course', models.BooleanField(default=False)),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True, help_text='Assignment notes or special instructions')),
                ('assigned_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='instructor_assignments_made', to=settings.AUTH_USER_MODEL)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='instructor_assignments', to='courses.course')),
                ('instructor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='course_assignments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Course Instructor Assignment',
                'verbose_name_plural': 'Course Instructor Assignments',
                'ordering': ['-assigned_at'],
                'unique_together': {('course', 'instructor')},
            },
        ),
    ]
