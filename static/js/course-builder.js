// Course Builder JavaScript
class CourseBuilder {
    constructor() {
        this.currentStep = parseInt(document.getElementById('currentStep')?.value || 1);
        this.sessionId = document.getElementById('sessionId')?.value || null;
        this.courseId = document.getElementById('courseId')?.value || null;
        this.autoSaveInterval = null;
        this.courseData = {};
        
        this.init();
    }
    
    init() {
        this.setupAutoSave();
        this.setupDragAndDrop();
        this.loadSessionData();
        
        // Setup form validation
        this.setupFormValidation();
        
        // Setup event listeners
        this.setupEventListeners();
    }
    
    setupAutoSave() {
        // Auto-save every 30 seconds
        this.autoSaveInterval = setInterval(() => {
            this.saveSession();
        }, 30000);
        
        // Save on form changes
        document.addEventListener('input', (e) => {
            if (e.target.form) {
                clearTimeout(this.saveTimeout);
                this.saveTimeout = setTimeout(() => {
                    this.saveSession();
                }, 2000);
            }
        });
    }
    
    setupDragAndDrop() {
        // Setup sortable for modules
        const moduleContainer = document.getElementById('courseStructure');
        if (moduleContainer) {
            new Sortable(moduleContainer, {
                handle: '.drag-handle',
                animation: 150,
                onEnd: () => {
                    this.updateModuleOrder();
                }
            });
        }
    }
    
    setupEventListeners() {
        // Course title and description preview updates
        const titleInput = document.getElementById('courseTitle');
        const descInput = document.getElementById('courseDescription');
        const priceInput = document.getElementById('coursePrice');
        const durationInput = document.getElementById('estimatedDuration');
        
        if (titleInput) {
            titleInput.addEventListener('input', (e) => {
                document.getElementById('previewTitle').textContent = e.target.value || 'Course Title';
            });
        }
        
        if (descInput) {
            descInput.addEventListener('input', (e) => {
                document.getElementById('previewDescription').textContent = e.target.value || 'Course description will appear here...';
            });
        }
        
        if (priceInput) {
            priceInput.addEventListener('input', (e) => {
                const price = parseFloat(e.target.value) || 0;
                document.getElementById('previewPrice').textContent = price > 0 ? `$${price}` : 'Free';
            });
        }
        
        if (durationInput) {
            durationInput.addEventListener('input', (e) => {
                document.getElementById('previewDuration').textContent = `${e.target.value || 10} hours`;
            });
        }
    }
    
    setupFormValidation() {
        // Add Bootstrap validation classes
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }
    
    async saveSession() {
        try {
            const sessionData = this.collectSessionData();
            
            const formData = new FormData();
            formData.append('action', 'save_session');
            formData.append('session_data', JSON.stringify(sessionData));
            formData.append('current_step', this.currentStep);
            formData.append('session_id', this.sessionId || '');
            formData.append('csrfmiddlewaretoken', this.getCSRFToken());
            
            const response = await fetch('/course-builder/api/', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.sessionId = result.session_id;
                document.getElementById('sessionId').value = this.sessionId;
                this.showAutoSaveIndicator();
            }
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    }
    
    collectSessionData() {
        const data = {};
        
        // Collect data based on current step
        if (this.currentStep === 1) {
            const form = document.getElementById('courseBasicsForm');
            if (form) {
                data.basics = {
                    title: form.title?.value || '',
                    description: form.description?.value || '',
                    category: form.category?.value || '',
                    difficulty_level: form.difficulty_level?.value || 'beginner',
                    estimated_duration: parseInt(form.estimated_duration?.value) || 10
                };
            }
        } else if (this.currentStep === 2) {
            data.structure = this.collectStructureData();
        } else if (this.currentStep === 5) {
            const form = document.getElementById('publishingForm');
            if (form) {
                data.publishing = {
                    price: parseFloat(form.price?.value) || 0,
                    enrollment_limit: form.enrollment_limit?.value || null,
                    is_featured: form.is_featured?.checked || false
                };
            }
        }
        
        return data;
    }
    
    collectStructureData() {
        const modules = [];
        const moduleElements = document.querySelectorAll('.module-item');
        
        moduleElements.forEach((moduleEl, index) => {
            const moduleData = {
                title: moduleEl.querySelector('.module-title')?.value || '',
                description: moduleEl.querySelector('.module-description')?.value || '',
                sort_order: index,
                lessons: []
            };
            
            const lessonElements = moduleEl.querySelectorAll('.lesson-item');
            lessonElements.forEach((lessonEl, lessonIndex) => {
                moduleData.lessons.push({
                    title: lessonEl.querySelector('.lesson-title')?.value || '',
                    content_type: lessonEl.querySelector('.lesson-type')?.value || 'text',
                    sort_order: lessonIndex
                });
            });
            
            modules.push(moduleData);
        });
        
        return modules;
    }
    
    showAutoSaveIndicator() {
        const indicator = document.getElementById('autoSaveIndicator');
        if (indicator) {
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 2000);
        }
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
    
    loadSessionData() {
        // Load existing session data if available
        if (this.sessionId) {
            // Implementation for loading session data
        }
    }
    
    updateModuleOrder() {
        // Update module order after drag and drop
        const moduleElements = document.querySelectorAll('.module-item');
        moduleElements.forEach((el, index) => {
            el.dataset.order = index;
        });
        this.saveSession();
    }
}

// Global functions for template usage
function nextStep() {
    const builder = window.courseBuilder;
    if (builder.currentStep < 5) {
        if (builder.validateCurrentStep()) {
            window.location.href = `/course-builder/wizard/step/${builder.currentStep + 1}/?session=${builder.sessionId}`;
        }
    }
}

function previousStep() {
    const builder = window.courseBuilder;
    if (builder.currentStep > 1) {
        window.location.href = `/course-builder/wizard/step/${builder.currentStep - 1}/?session=${builder.sessionId}`;
    }
}

function saveDraft() {
    const builder = window.courseBuilder;
    builder.saveSession().then(() => {
        alert('Draft saved successfully!');
    });
}

function addModule() {
    const container = document.getElementById('courseStructure');
    const moduleCount = container.children.length;
    
    const moduleHTML = `
        <div class="module-item" data-order="${moduleCount}">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-grip-vertical drag-handle"></i>
                <h5 class="mb-0 ms-2">Module ${moduleCount + 1}</h5>
                <button type="button" class="btn btn-sm btn-outline-danger ms-auto" onclick="removeModule(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <input type="text" class="form-control module-title" placeholder="Module title" required>
                </div>
                <div class="col-md-6">
                    <input type="text" class="form-control module-description" placeholder="Module description">
                </div>
            </div>
            <div class="lessons-container mt-3">
                <!-- Lessons will be added here -->
            </div>
            <button type="button" class="btn btn-add-lesson mt-2" onclick="addLesson(this)">
                <i class="fas fa-plus me-2"></i>
                Add Lesson
            </button>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', moduleHTML);
    
    // Setup sortable for lessons in this module
    const newModule = container.lastElementChild;
    const lessonsContainer = newModule.querySelector('.lessons-container');
    new Sortable(lessonsContainer, {
        handle: '.drag-handle',
        animation: 150
    });
}

function addLesson(button) {
    const lessonsContainer = button.previousElementSibling;
    const lessonCount = lessonsContainer.children.length;
    
    const lessonHTML = `
        <div class="lesson-item">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-grip-vertical drag-handle"></i>
                <span class="ms-2">Lesson ${lessonCount + 1}</span>
                <button type="button" class="btn btn-sm btn-outline-danger ms-auto" onclick="removeLesson(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <input type="text" class="form-control lesson-title" placeholder="Lesson title" required>
                </div>
                <div class="col-md-6">
                    <select class="form-select lesson-type">
                        <option value="text">Text Content</option>
                        <option value="video">Video</option>
                        <option value="document">Document</option>
                        <option value="quiz">Quiz</option>
                    </select>
                </div>
            </div>
        </div>
    `;
    
    lessonsContainer.insertAdjacentHTML('beforeend', lessonHTML);
}

function removeModule(button) {
    if (confirm('Are you sure you want to remove this module?')) {
        button.closest('.module-item').remove();
    }
}

function removeLesson(button) {
    if (confirm('Are you sure you want to remove this lesson?')) {
        button.closest('.lesson-item').remove();
    }
}

function loadTemplate(templateId) {
    // Implementation for loading course template
    console.log('Loading template:', templateId);
}

// Media and Template Functions
function openMediaLibrary() {
    window.open('/course-builder/media/', 'mediaLibrary', 'width=1000,height=700,scrollbars=yes');
}

function insertMediaIntoEditor(url, type) {
    // Insert media into TinyMCE editor
    if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
        if (type.startsWith('image/')) {
            tinymce.activeEditor.insertContent(`<img src="${url}" alt="Course Image" style="max-width: 100%; height: auto;">`);
        } else if (type.startsWith('video/')) {
            tinymce.activeEditor.insertContent(`<video controls style="max-width: 100%;"><source src="${url}" type="${type}">Your browser does not support the video tag.</video>`);
        } else {
            tinymce.activeEditor.insertContent(`<a href="${url}" target="_blank">Download File</a>`);
        }
    }
}

function insertTemplate(templateId) {
    // Fetch template content and insert into editor
    fetch(`/course-builder/api/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=get_template&template_id=${templateId}&csrfmiddlewaretoken=${getCSRFToken()}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && typeof tinymce !== 'undefined' && tinymce.activeEditor) {
            tinymce.activeEditor.insertContent(data.content);
        }
    })
    .catch(error => console.error('Error inserting template:', error));
}

function saveLessonContent() {
    const lessonId = document.getElementById('lessonSelector')?.value;
    if (!lessonId) {
        alert('Please select a lesson first');
        return;
    }

    let content = '';
    if (typeof tinymce !== 'undefined' && tinymce.get('lessonContent')) {
        content = tinymce.get('lessonContent').getContent();
    } else {
        content = document.getElementById('lessonContent')?.value || '';
    }

    const videoUrl = document.getElementById('videoUrl')?.value || '';
    const duration = document.getElementById('lessonDuration')?.value || 30;

    // Save via AJAX
    const formData = new FormData();
    formData.append('action', 'add_content');
    formData.append('lesson_id', lessonId);
    formData.append('content', content);
    formData.append('video_url', videoUrl);
    formData.append('duration', duration);
    formData.append('csrfmiddlewaretoken', getCSRFToken());

    fetch('/course-builder/api/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Content saved successfully!', 'success');
        } else {
            showNotification('Error: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Save error:', error);
        showNotification('Failed to save content', 'error');
    });
}

function loadLessonContent() {
    const lessonId = document.getElementById('lessonSelector')?.value;
    const editor = document.getElementById('lessonContentEditor');

    if (lessonId && editor) {
        editor.style.display = 'block';
        // Load existing content if available
        // This would fetch lesson content from the server
    } else if (editor) {
        editor.style.display = 'none';
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '120px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

function getCSRFToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
}

// Bulk Content Import Functions
function importFromWord() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.doc,.docx';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            // Implementation for Word import
            console.log('Importing Word document:', file.name);
            showNotification('Word import feature coming soon!', 'info');
        }
    };
    input.click();
}

function importFromPDF() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            // Implementation for PDF import
            console.log('Importing PDF:', file.name);
            showNotification('PDF import feature coming soon!', 'info');
        }
    };
    input.click();
}

// Enhanced Course Structure Functions
function duplicateModule(moduleElement) {
    const clone = moduleElement.cloneNode(true);
    const moduleTitle = clone.querySelector('.module-title');
    if (moduleTitle) {
        moduleTitle.value = moduleTitle.value + ' (Copy)';
    }
    moduleElement.parentNode.insertBefore(clone, moduleElement.nextSibling);
    updateModuleNumbers();
}

function duplicateLesson(lessonElement) {
    const clone = lessonElement.cloneNode(true);
    const lessonTitle = clone.querySelector('.lesson-title');
    if (lessonTitle) {
        lessonTitle.value = lessonTitle.value + ' (Copy)';
    }
    lessonElement.parentNode.insertBefore(clone, lessonElement.nextSibling);
    updateLessonNumbers();
}

function updateModuleNumbers() {
    const modules = document.querySelectorAll('.module-item');
    modules.forEach((module, index) => {
        const header = module.querySelector('h5');
        if (header) {
            header.textContent = `Module ${index + 1}`;
        }
    });
}

function updateLessonNumbers() {
    const modules = document.querySelectorAll('.module-item');
    modules.forEach(module => {
        const lessons = module.querySelectorAll('.lesson-item');
        lessons.forEach((lesson, index) => {
            const header = lesson.querySelector('span');
            if (header) {
                header.textContent = `Lesson ${index + 1}`;
            }
        });
    });
}

// Initialize course builder when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.courseBuilder = new CourseBuilder();
});
