// Course Builder JavaScript
class CourseBuilder {
    constructor() {
        this.currentStep = parseInt(document.getElementById('currentStep')?.value || 1);
        this.sessionId = document.getElementById('sessionId')?.value || null;
        this.courseId = document.getElementById('courseId')?.value || null;
        this.autoSaveInterval = null;
        this.courseData = {};
        
        this.init();
    }
    
    init() {
        this.setupAutoSave();
        this.setupDragAndDrop();
        this.loadSessionData();
        
        // Setup form validation
        this.setupFormValidation();
        
        // Setup event listeners
        this.setupEventListeners();
    }
    
    setupAutoSave() {
        // Auto-save every 30 seconds
        this.autoSaveInterval = setInterval(() => {
            this.saveSession();
        }, 30000);
        
        // Save on form changes
        document.addEventListener('input', (e) => {
            if (e.target.form) {
                clearTimeout(this.saveTimeout);
                this.saveTimeout = setTimeout(() => {
                    this.saveSession();
                }, 2000);
            }
        });
    }
    
    setupDragAndDrop() {
        // Setup sortable for modules
        const moduleContainer = document.getElementById('courseStructure');
        if (moduleContainer) {
            new Sortable(moduleContainer, {
                handle: '.drag-handle',
                animation: 150,
                onEnd: () => {
                    this.updateModuleOrder();
                }
            });
        }
    }
    
    setupEventListeners() {
        // Course title and description preview updates
        const titleInput = document.getElementById('courseTitle');
        const descInput = document.getElementById('courseDescription');
        const priceInput = document.getElementById('coursePrice');
        const durationInput = document.getElementById('estimatedDuration');
        
        if (titleInput) {
            titleInput.addEventListener('input', (e) => {
                document.getElementById('previewTitle').textContent = e.target.value || 'Course Title';
            });
        }
        
        if (descInput) {
            descInput.addEventListener('input', (e) => {
                document.getElementById('previewDescription').textContent = e.target.value || 'Course description will appear here...';
            });
        }
        
        if (priceInput) {
            priceInput.addEventListener('input', (e) => {
                const price = parseFloat(e.target.value) || 0;
                document.getElementById('previewPrice').textContent = price > 0 ? `$${price}` : 'Free';
            });
        }
        
        if (durationInput) {
            durationInput.addEventListener('input', (e) => {
                document.getElementById('previewDuration').textContent = `${e.target.value || 10} hours`;
            });
        }
    }
    
    setupFormValidation() {
        // Add Bootstrap validation classes
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }
    
    async saveSession() {
        try {
            const sessionData = this.collectSessionData();
            
            const formData = new FormData();
            formData.append('action', 'save_session');
            formData.append('session_data', JSON.stringify(sessionData));
            formData.append('current_step', this.currentStep);
            formData.append('session_id', this.sessionId || '');
            formData.append('csrfmiddlewaretoken', this.getCSRFToken());
            
            const response = await fetch('/course-builder/api/', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.sessionId = result.session_id;
                document.getElementById('sessionId').value = this.sessionId;
                this.showAutoSaveIndicator();
            }
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    }
    
    collectSessionData() {
        const data = {};
        
        // Collect data based on current step
        if (this.currentStep === 1) {
            const form = document.getElementById('courseBasicsForm');
            if (form) {
                data.basics = {
                    title: form.title?.value || '',
                    description: form.description?.value || '',
                    category: form.category?.value || '',
                    difficulty_level: form.difficulty_level?.value || 'beginner',
                    estimated_duration: parseInt(form.estimated_duration?.value) || 10
                };
            }
        } else if (this.currentStep === 2) {
            data.structure = this.collectStructureData();
        } else if (this.currentStep === 5) {
            const form = document.getElementById('publishingForm');
            if (form) {
                data.publishing = {
                    price: parseFloat(form.price?.value) || 0,
                    enrollment_limit: form.enrollment_limit?.value || null,
                    is_featured: form.is_featured?.checked || false
                };
            }
        }
        
        return data;
    }
    
    collectStructureData() {
        const modules = [];
        const moduleElements = document.querySelectorAll('.module-item');
        
        moduleElements.forEach((moduleEl, index) => {
            const moduleData = {
                title: moduleEl.querySelector('.module-title')?.value || '',
                description: moduleEl.querySelector('.module-description')?.value || '',
                sort_order: index,
                lessons: []
            };
            
            const lessonElements = moduleEl.querySelectorAll('.lesson-item');
            lessonElements.forEach((lessonEl, lessonIndex) => {
                moduleData.lessons.push({
                    title: lessonEl.querySelector('.lesson-title')?.value || '',
                    content_type: lessonEl.querySelector('.lesson-type')?.value || 'text',
                    sort_order: lessonIndex
                });
            });
            
            modules.push(moduleData);
        });
        
        return modules;
    }
    
    showAutoSaveIndicator() {
        const indicator = document.getElementById('autoSaveIndicator');
        if (indicator) {
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 2000);
        }
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
    
    loadSessionData() {
        // Load existing session data if available
        if (this.sessionId) {
            // Implementation for loading session data
        }
    }
    
    updateModuleOrder() {
        // Update module order after drag and drop
        const moduleElements = document.querySelectorAll('.module-item');
        moduleElements.forEach((el, index) => {
            el.dataset.order = index;
        });
        this.saveSession();
    }
}

// Global functions for template usage
function nextStep() {
    const builder = window.courseBuilder;
    if (builder.currentStep < 5) {
        if (builder.validateCurrentStep()) {
            window.location.href = `/course-builder/wizard/step/${builder.currentStep + 1}/?session=${builder.sessionId}`;
        }
    }
}

function previousStep() {
    const builder = window.courseBuilder;
    if (builder.currentStep > 1) {
        window.location.href = `/course-builder/wizard/step/${builder.currentStep - 1}/?session=${builder.sessionId}`;
    }
}

function saveDraft() {
    const builder = window.courseBuilder;
    builder.saveSession().then(() => {
        alert('Draft saved successfully!');
    });
}

function addModule() {
    const container = document.getElementById('courseStructure');
    const moduleCount = container.children.length;
    
    const moduleHTML = `
        <div class="module-item" data-order="${moduleCount}">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-grip-vertical drag-handle"></i>
                <h5 class="mb-0 ms-2">Module ${moduleCount + 1}</h5>
                <button type="button" class="btn btn-sm btn-outline-danger ms-auto" onclick="removeModule(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <input type="text" class="form-control module-title" placeholder="Module title" required>
                </div>
                <div class="col-md-6">
                    <input type="text" class="form-control module-description" placeholder="Module description">
                </div>
            </div>
            <div class="lessons-container mt-3">
                <!-- Lessons will be added here -->
            </div>
            <button type="button" class="btn btn-add-lesson mt-2" onclick="addLesson(this)">
                <i class="fas fa-plus me-2"></i>
                Add Lesson
            </button>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', moduleHTML);
    
    // Setup sortable for lessons in this module
    const newModule = container.lastElementChild;
    const lessonsContainer = newModule.querySelector('.lessons-container');
    new Sortable(lessonsContainer, {
        handle: '.drag-handle',
        animation: 150
    });
}

function addLesson(button) {
    const lessonsContainer = button.previousElementSibling;
    const lessonCount = lessonsContainer.children.length;
    
    const lessonHTML = `
        <div class="lesson-item">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-grip-vertical drag-handle"></i>
                <span class="ms-2">Lesson ${lessonCount + 1}</span>
                <button type="button" class="btn btn-sm btn-outline-danger ms-auto" onclick="removeLesson(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <input type="text" class="form-control lesson-title" placeholder="Lesson title" required>
                </div>
                <div class="col-md-6">
                    <select class="form-select lesson-type">
                        <option value="text">Text Content</option>
                        <option value="video">Video</option>
                        <option value="document">Document</option>
                        <option value="quiz">Quiz</option>
                    </select>
                </div>
            </div>
        </div>
    `;
    
    lessonsContainer.insertAdjacentHTML('beforeend', lessonHTML);
}

function removeModule(button) {
    if (confirm('Are you sure you want to remove this module?')) {
        button.closest('.module-item').remove();
    }
}

function removeLesson(button) {
    if (confirm('Are you sure you want to remove this lesson?')) {
        button.closest('.lesson-item').remove();
    }
}

function loadTemplate(templateId) {
    // Implementation for loading course template
    console.log('Loading template:', templateId);
}

// Initialize course builder when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.courseBuilder = new CourseBuilder();
});
