services:
  - type: web
    name: yitp-lms
    env: python
    branch: livexp2
    buildCommand: ./build.sh
    startCommand: gunicorn blog.wsgi:application
    envVars:
      # Django Configuration
      - key: DEBUG
        value: False
      - key: SECRET_KEY
        generateValue: true
      - key: ALLOWED_HOSTS
        value: "yitp-lms.onrender.com,*.onrender.com"

      # Neon PostgreSQL Database Configuration
      - key: DB_NAME
        value: yitplms
      - key: DB_USER
        value: yitplms_owner
      - key: DB_PASSWORD
        value: npg_LwHI4a8TufWb
      - key: DB_HOST
        value: ep-spring-block-a5drxziv-pooler.us-east-2.aws.neon.tech
      - key: DB_PORT
        value: 5432

      # Email Configuration (Gmail SMTP)
      - key: EMAIL_HOST_USER
        value: "<EMAIL>"
      - key: EMAIL_HOST_PASSWORD
        value: "roqu frlt wvof rqxk"

      # M-Pesa API Configuration
      - key: MPESA_CONSUMER_KEY
        value: "UMi2MLMFIdaOS8vRFiWLG40CJ4GzWAGAHbwFROxe473iZ6gQ"
      - key: MPESA_CONSUMER_SECRET
        value: "2K3IJkdE7uxLJm9Nis3mhO3TZHqmowc0ndI9abTGxGJ4gcAdvdAZ5C9tx9wrAWRp"
      - key: MPESA_ENVIRONMENT
        value: "sandbox"
      - key: MPESA_SHORTCODE
        value: "174379"
      - key: MPESA_PASSKEY
        value: "bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919"

      # Site Configuration
      - key: SITE_URL
        value: "https://yitp-lms.onrender.com"

      # Admin Configuration
      - key: ADMIN_EMAIL
        value: "<EMAIL>"

      # TinyMCE Configuration
      - key: TINYMCE_API_KEY
        value: "qu2jb8k2dyah1y5pjdglgob206f26juotj3u82hzd7mvyz1x"

      # Static Files Configuration
      - key: STATIC_URL
        value: "/static/"
      - key: STATIC_ROOT
        value: "staticfiles"