[{"model": "contenttypes.contenttype", "pk": 1, "fields": {"app_label": "jet", "model": "bookmark"}}, {"model": "contenttypes.contenttype", "pk": 2, "fields": {"app_label": "jet", "model": "pinnedapplication"}}, {"model": "contenttypes.contenttype", "pk": 3, "fields": {"app_label": "users", "model": "editpage"}}, {"model": "contenttypes.contenttype", "pk": 4, "fields": {"app_label": "users", "model": "mainprogrames"}}, {"model": "contenttypes.contenttype", "pk": 5, "fields": {"app_label": "users", "model": "secondsection"}}, {"model": "contenttypes.contenttype", "pk": 6, "fields": {"app_label": "users", "model": "secondsectionbox"}}, {"model": "contenttypes.contenttype", "pk": 7, "fields": {"app_label": "users", "model": "secondsectionicon"}}, {"model": "contenttypes.contenttype", "pk": 8, "fields": {"app_label": "users", "model": "sponsorshiprequest"}}, {"model": "contenttypes.contenttype", "pk": 9, "fields": {"app_label": "users", "model": "profile"}}, {"model": "contenttypes.contenttype", "pk": 10, "fields": {"app_label": "users", "model": "otpverification"}}, {"model": "contenttypes.contenttype", "pk": 11, "fields": {"app_label": "blogapp", "model": "category"}}, {"model": "contenttypes.contenttype", "pk": 12, "fields": {"app_label": "blogapp", "model": "staticcontent"}}, {"model": "contenttypes.contenttype", "pk": 13, "fields": {"app_label": "blogapp", "model": "post"}}, {"model": "contenttypes.contenttype", "pk": 14, "fields": {"app_label": "blogapp", "model": "comment"}}, {"model": "contenttypes.contenttype", "pk": 15, "fields": {"app_label": "events", "model": "event"}}, {"model": "contenttypes.contenttype", "pk": 16, "fields": {"app_label": "events", "model": "eventcategory"}}, {"model": "contenttypes.contenttype", "pk": 17, "fields": {"app_label": "events", "model": "eventcomment"}}, {"model": "contenttypes.contenttype", "pk": 18, "fields": {"app_label": "courses", "model": "coursetag"}}, {"model": "contenttypes.contenttype", "pk": 19, "fields": {"app_label": "courses", "model": "category"}}, {"model": "contenttypes.contenttype", "pk": 20, "fields": {"app_label": "courses", "model": "course"}}, {"model": "contenttypes.contenttype", "pk": 21, "fields": {"app_label": "courses", "model": "module"}}, {"model": "contenttypes.contenttype", "pk": 22, "fields": {"app_label": "courses", "model": "lesson"}}, {"model": "contenttypes.contenttype", "pk": 23, "fields": {"app_label": "courses", "model": "coursereview"}}, {"model": "contenttypes.contenttype", "pk": 24, "fields": {"app_label": "courses", "model": "coursetagging"}}, {"model": "contenttypes.contenttype", "pk": 25, "fields": {"app_label": "progress", "model": "achievement"}}, {"model": "contenttypes.contenttype", "pk": 26, "fields": {"app_label": "progress", "model": "enrollment"}}, {"model": "contenttypes.contenttype", "pk": 27, "fields": {"app_label": "progress", "model": "learningpath"}}, {"model": "contenttypes.contenttype", "pk": 28, "fields": {"app_label": "progress", "model": "learningpathcourse"}}, {"model": "contenttypes.contenttype", "pk": 29, "fields": {"app_label": "progress", "model": "quizattempt"}}, {"model": "contenttypes.contenttype", "pk": 30, "fields": {"app_label": "progress", "model": "studysession"}}, {"model": "contenttypes.contenttype", "pk": 31, "fields": {"app_label": "progress", "model": "assignmentsubmission"}}, {"model": "contenttypes.contenttype", "pk": 32, "fields": {"app_label": "progress", "model": "lessonprogress"}}, {"model": "contenttypes.contenttype", "pk": 33, "fields": {"app_label": "progress", "model": "certificate"}}, {"model": "contenttypes.contenttype", "pk": 34, "fields": {"app_label": "assessments", "model": "gradingscale"}}, {"model": "contenttypes.contenttype", "pk": 35, "fields": {"app_label": "assessments", "model": "assessmenttemplate"}}, {"model": "contenttypes.contenttype", "pk": 36, "fields": {"app_label": "assessments", "model": "assignment"}}, {"model": "contenttypes.contenttype", "pk": 37, "fields": {"app_label": "assessments", "model": "peerreview"}}, {"model": "contenttypes.contenttype", "pk": 38, "fields": {"app_label": "assessments", "model": "quiz"}}, {"model": "contenttypes.contenttype", "pk": 39, "fields": {"app_label": "assessments", "model": "question"}}, {"model": "contenttypes.contenttype", "pk": 40, "fields": {"app_label": "assessments", "model": "rubriccriteria"}}, {"model": "contenttypes.contenttype", "pk": 41, "fields": {"app_label": "assessments", "model": "selfassessment"}}, {"model": "contenttypes.contenttype", "pk": 42, "fields": {"app_label": "communication", "model": "announcement"}}, {"model": "contenttypes.contenttype", "pk": 43, "fields": {"app_label": "communication", "model": "feedback"}}, {"model": "contenttypes.contenttype", "pk": 44, "fields": {"app_label": "communication", "model": "forum"}}, {"model": "contenttypes.contenttype", "pk": 45, "fields": {"app_label": "communication", "model": "message"}}, {"model": "contenttypes.contenttype", "pk": 46, "fields": {"app_label": "communication", "model": "notification"}}, {"model": "contenttypes.contenttype", "pk": 47, "fields": {"app_label": "communication", "model": "studygroup"}}, {"model": "contenttypes.contenttype", "pk": 48, "fields": {"app_label": "communication", "model": "studygroupmembership"}}, {"model": "contenttypes.contenttype", "pk": 49, "fields": {"app_label": "communication", "model": "topic"}}, {"model": "contenttypes.contenttype", "pk": 50, "fields": {"app_label": "communication", "model": "reply"}}, {"model": "contenttypes.contenttype", "pk": 51, "fields": {"app_label": "content", "model": "contentitem"}}, {"model": "contenttypes.contenttype", "pk": 52, "fields": {"app_label": "content", "model": "contentlibrary"}}, {"model": "contenttypes.contenttype", "pk": 53, "fields": {"app_label": "content", "model": "interactiveexercise"}}, {"model": "contenttypes.contenttype", "pk": 54, "fields": {"app_label": "content", "model": "resource"}}, {"model": "contenttypes.contenttype", "pk": 55, "fields": {"app_label": "content", "model": "lessoncontent"}}, {"model": "contenttypes.contenttype", "pk": 56, "fields": {"app_label": "content", "model": "libraryitem"}}, {"model": "contenttypes.contenttype", "pk": 57, "fields": {"app_label": "content", "model": "lessonresource"}}, {"model": "contenttypes.contenttype", "pk": 58, "fields": {"app_label": "payments", "model": "payment"}}, {"model": "contenttypes.contenttype", "pk": 59, "fields": {"app_label": "payments", "model": "paymentmethod"}}, {"model": "contenttypes.contenttype", "pk": 60, "fields": {"app_label": "payments", "model": "paymentcallback"}}, {"model": "contenttypes.contenttype", "pk": 61, "fields": {"app_label": "payments", "model": "paymentnotification"}}, {"model": "contenttypes.contenttype", "pk": 62, "fields": {"app_label": "analytics", "model": "paymentanalytics"}}, {"model": "contenttypes.contenttype", "pk": 63, "fields": {"app_label": "analytics", "model": "userpaymentbehavior"}}, {"model": "contenttypes.contenttype", "pk": 64, "fields": {"app_label": "analytics", "model": "courserevenueanalytics"}}, {"model": "contenttypes.contenttype", "pk": 65, "fields": {"app_label": "admin", "model": "logentry"}}, {"model": "contenttypes.contenttype", "pk": 66, "fields": {"app_label": "auth", "model": "permission"}}, {"model": "contenttypes.contenttype", "pk": 67, "fields": {"app_label": "auth", "model": "group"}}, {"model": "contenttypes.contenttype", "pk": 68, "fields": {"app_label": "auth", "model": "user"}}, {"model": "contenttypes.contenttype", "pk": 69, "fields": {"app_label": "contenttypes", "model": "contenttype"}}, {"model": "contenttypes.contenttype", "pk": 70, "fields": {"app_label": "sessions", "model": "session"}}, {"model": "contenttypes.contenttype", "pk": 71, "fields": {"app_label": "sites", "model": "site"}}, {"model": "contenttypes.contenttype", "pk": 72, "fields": {"app_label": "taggit", "model": "tag"}}, {"model": "contenttypes.contenttype", "pk": 73, "fields": {"app_label": "taggit", "model": "taggeditem"}}, {"model": "contenttypes.contenttype", "pk": 74, "fields": {"app_label": "assessments", "model": "assignmentsubmission"}}, {"model": "contenttypes.contenttype", "pk": 75, "fields": {"app_label": "assessments", "model": "selfassessmentresponse"}}, {"model": "contenttypes.contenttype", "pk": 76, "fields": {"app_label": "users", "model": "instructorprofile"}}, {"model": "contenttypes.contenttype", "pk": 77, "fields": {"app_label": "users", "model": "specialization"}}, {"model": "contenttypes.contenttype", "pk": 78, "fields": {"app_label": "users", "model": "courseinstructor"}}]